﻿using goodkey_cms.DTO;
using goodkey_cms.Repositories;
using goodkey_common.DTO;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class PermissionsController : ControllerBase
    {
        private readonly IPermissionRepository _repo;
        public PermissionsController(IPermissionRepository repo)
        {
            _repo = repo;
        }

        [HttpGet]
        public GenericRespond<IEnumerable<PermissionDetail>> Get()
        {
            return new()
            {
                Data = _repo.GetAll().Select(item =>

                new PermissionDetail()
                {
                    Id = item.PermissionId,
                    Name = item.Name,
                    Code = item.Code
                }
            )
            };
        }
    }
}
