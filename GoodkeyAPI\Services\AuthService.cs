﻿using goodkey_cms.DTO.Auth;
using goodkey_cms.Infrastructure.Binders;
using goodkey_cms.Infrastructure.Constants;
using goodkey_cms.Infrastructure.Helpers;
using goodkey_cms.Infrastructure.Utils;
using goodkey_cms.Repositories;
using goodkey_common.Models;
using goodkey_common.Services;
using HandlebarsDotNet;
using System.Security.Claims;

namespace goodkey_cms.Services
{
    public class AuthService
    {
        private readonly IUserRepository _repo;
        private readonly IMailService _mail;
        private readonly JwtService _jwt;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public AuthService(IUserRepository repo, JwtService jwt, IHttpContextAccessor httpContextAccessor, IMailService mail)
        {
            _repo = repo;
            _mail = mail;
            _jwt = jwt;
            _httpContextAccessor = httpContextAccessor;
        }

        public void ClientFirstLogin(string loginEmail, string lang)
        {
            var user = _repo.GetUserByUsername(loginEmail);
            var verificationToken = Guid.NewGuid().ToString("N");
            _repo.SetVerificationToken((int)user.UserId, verificationToken);
            var content = Handlebars.Compile(Emails.NewAccountCreated)(new
            {
                VerificationLink = "https://admingoodkey.malopan.com/account-verification?key=" + verificationToken,
                Username = loginEmail,
            });

            var NewAccountCreatedSubjectEn = "GoodKey - Your GoodKey Account Is Ready!";
            var NewAccountCreatedSubjectFr = "Goodkey - Votre compte Goodkey est prêt!";

            _mail.Send(loginEmail, lang.Equals("en") ? NewAccountCreatedSubjectEn : NewAccountCreatedSubjectFr, EmailHelper.generateEmail(content));
        }


        public bool RequestReset(string username)
        {
            var user = _repo.GetUserByUsername(username);
            if (user != null)
            {
                var verificationToken = Guid.NewGuid().ToString("N");
                _repo.SetVerificationToken((int)user.UserId, verificationToken);
                var content = Handlebars.Compile(Emails.ResetPassword)(new
                {
                    VerificationLink = "https://admingoodkey.malopan.com/reset-password?key=" + verificationToken
                });
                _mail.Send(user.VerificationEmail, "Goodkey Show Services - Reset Password", EmailHelper.generateEmail(content));
                return true;
            }
            return false;
        }

        public AuthUser? Current
        {
            get
            {
                var current = _httpContextAccessor.HttpContext?.GetUser();
                if (current == null)
                {
                    var username = _httpContextAccessor.HttpContext?.GetUsername() ?? _httpContextAccessor.HttpContext?.User.Claims?.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value;// Add manual find In case we reach here before middlewere
                    if (username == null) return null;
                    current = _repo.GetUserByUsername(username);
                    if (current == null) return null;
                    return _httpContextAccessor.HttpContext?.SetUser(current);
                }
                else return current;
            }
        }

        public AuthUser? GetUserByToken(string key)
        {
            return _repo.GetUserByToken(key);
        }


        public JwtService.Tokens? Verify(string key, string password)
        {
            var user = _repo.GetUserByToken(key);
            if (user != null && user.VerificationSentDate != null && user.VerificationSentDate > DateTime.UtcNow.AddDays(-7))
            {
                var tokens = _jwt.GenerateJwtToken(user.Username);
                _repo.SetVerified(user.UserId, HashUtility.HashPassword(password));
                return tokens;
            }
            return null;
        }

        public JwtService.Tokens? Login(LoginCredintals data)
        {
            var user = _repo.GetUserByUsername(data.Username);

            if (user != null && user.PasswordHash != null && HashUtility.VerifyPassword(data.Password, user.PasswordHash) && user.IsActive == true && user.IsVerified == true)
            {
                var tokens = _jwt.GenerateJwtToken(data.Username);
                //TODO store refrech token for token rotation
                return tokens;
            }
            return null;
        }

        public bool? ChangePassword(string username, string oldPassword, string password)
        {
            var user = _repo.GetUserByUsername(username);
            if (user != null && user.PasswordHash != null && HashUtility.VerifyPassword(oldPassword, user.PasswordHash) && user.IsActive == true && user.IsVerified == true)
            {
                var tokens = _repo.SetPassword(user.UserId, HashUtility.HashPassword(password));
                //TODO store refrech token for token rotation
                return true;
            }
            return false;
        }

        public void SendInviteEmail(string email, string token)
        {
            var content = Handlebars.Compile(Emails.EmployeeInvite)(new
            {
                VerificationLink = "https://admingoodkey.malopan.com/account-verification?key=" + token,
            });



            _mail.Send(email, "Goodkey - Invitation to Join Our Team!", EmailHelper.generateEmail(content));
        }
    }
}
