﻿using System.Security.Cryptography;
using System.Text;

namespace goodkey_cms.Infrastructure.Utils
{
    public static class HashUtility
    {
        private const int SaltSize = 16;
        private const int Iterations = 10000; // Adjust the number of iterations as needed for your application
        private const int HashSize = 32; // 256 bits

        //public static string HashPassword(string password)
        //{
        //    byte[] salt = GenerateSalt();
        //    byte[] hash = HashPasswordWithSalt(password, salt);
        //    return Convert.ToBase64String(salt) + ":" + Convert.ToBase64String(hash);
        //}
        static public string HashPassword(string password)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
            return BitConverter.ToString(hashedBytes).Replace("-", "").ToLower();
        }

        //public static bool VerifyPassword(string password, string hashedPassword)
        //{
        //    try
        //    {
        //        string[] parts = hashedPassword.Split(':');
        //        byte[] salt = Convert.FromBase64String(parts[0]);
        //        byte[] hash = Convert.FromBase64String(parts[1]);
        //        byte[] testHash = HashPasswordWithSalt(password, salt);

        //        return SlowEquals(hash, testHash);
        //    }
        //    catch (Exception)
        //    {
        //        return false;
        //    }
        //}

        static public bool VerifyPassword(string password, string hashedPassword)
        {
            return HashPassword(password) == hashedPassword;
        }

        private static byte[] GenerateSalt()
        {
            byte[] salt = new byte[SaltSize];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(salt);
            }
            return salt;
        }

        private static byte[] HashPasswordWithSalt(string password, byte[] salt)
        {
            using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, Iterations, HashAlgorithmName.SHA512);
            return pbkdf2.GetBytes(HashSize);
        }

        private static bool SlowEquals(byte[] a, byte[] b)
        {
            uint diff = (uint)a.Length ^ (uint)b.Length;
            for (int i = 0; i < a.Length && i < b.Length; i++)
            {
                diff |= (uint)(a[i] ^ b[i]);
            }
            return diff == 0;
        }

        public static string PasswordGenerator(string firstName, string lastName)
        {
            var _random = new Random();
            // Ensure the first name and last name are in lowercase and concatenate them
            string basePassword = (firstName + lastName).ToLower();

            // Generate 4 random digits
            string randomDigits = _random.Next(1000, 9999).ToString();

            // Combine the base password and random digits
            string rawPassword = basePassword + randomDigits;

            // Create a pool of characters to randomly choose from
            string upperCaseLetters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            string lowerCaseLetters = "abcdefghijklmnopqrstuvwxyz";
            string digits = "0123456789";
            string symbols = "!@#$%^&*()-_=+[]{}|;:,.<>?/~";

            // Ensure the password contains at least one uppercase letter, one symbol, and one digit
            char upperCaseLetter = upperCaseLetters[_random.Next(upperCaseLetters.Length)];
            char symbol = symbols[_random.Next(symbols.Length)];
            char digit = digits[_random.Next(digits.Length)];

            // Randomly shuffle the components of the password
            StringBuilder passwordBuilder = new StringBuilder(rawPassword);

            // Insert the characters at random positions
            passwordBuilder.Insert(_random.Next(passwordBuilder.Length), upperCaseLetter);
            passwordBuilder.Insert(_random.Next(passwordBuilder.Length), symbol);
            passwordBuilder.Insert(_random.Next(passwordBuilder.Length), digit);

            // Convert the string builder to a string and return the password
            return passwordBuilder.ToString();
        }

    }
}
