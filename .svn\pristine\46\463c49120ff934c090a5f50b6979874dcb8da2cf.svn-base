﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_cms.Repositories
{
    public interface IRoleRepository
    {
        IEnumerable<AuthRole> GetAll();
        AuthRole? Get(int id);
        int? Create(string name, string? description);
        bool Update(int id, string name, string? description);
        bool SetPermission(int id, IEnumerable<string> permissions);
        bool SetMenu(int id, IEnumerable<int> ids);
        IEnumerable<int> GetMenu(int id);
    }

    public class RoleRepository : IRoleRepository
    {
        private readonly GoodkeyContext _context;

        public RoleRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public int? Create(string name, string? description)
        {
            var item = new AuthRole()
            {
                Name = name,
                Description = description ?? string.Empty
            };

            _context.Add(item);
            _context.SaveChanges();
            return item.RoleId;
        }

        public AuthRole? Get(int id)
        {
            return _context.AuthRoles.Include(c => c.Permissions).FirstOrDefault(c => c.RoleId == id);
        }

        public IEnumerable<AuthRole> GetAll()
        {
            return _context.AuthRoles;
        }

        public bool SetPermission(int id, IEnumerable<string> permissions)
        {
            var item = _context.AuthRoles.Include(c => c.Permissions).FirstOrDefault(c => c.RoleId == id);
            if (item == null) return false;

            item.Permissions.Clear();

            foreach (var key in permissions)
            {
                var perm = _context.AuthPermissions.FirstOrDefault(c => c.Code == key);
                if (perm == null) return false;
                item.Permissions.Add(perm);
            }

            _context.SaveChanges();

            return true;
        }

        public bool Update(int id, string name, string? description)
        {
            var item = _context.AuthRoles.FirstOrDefault(c => c.RoleId == id);
            if (item == null) return false;

            item.Name = name;
            item.Description = description ?? string.Empty;

            _context.SaveChanges();
            return true;
        }

        public IEnumerable<int> GetMenu(int id)
        {
            return _context.AuthRoles.Include(c => c.MenuItems).FirstOrDefault(c => c.RoleId == id)?
                .MenuItems.Select(c => c.MenuItemId) ?? new List<int>();

        }

        public bool SetMenu(int id, IEnumerable<int> ids)
        {
            var item = _context.AuthRoles.Include(c => c.MenuItems).FirstOrDefault(c => c.RoleId == id);
            if (item == null) return false;
            item.MenuItems.Clear();
            foreach (var key in ids)
            {
                var perm = _context.MenuItems.FirstOrDefault(c => c.MenuItemId == key);
                if (perm == null) return false;
                item.MenuItems.Add(perm);
            }
            ;
            _context.SaveChanges();
            return true;
        }
    }
}

