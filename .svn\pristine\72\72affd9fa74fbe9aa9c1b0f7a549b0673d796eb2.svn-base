﻿namespace goodkey_cms.DTO.User
{
    public class UserData
    {
        public string Email { get; set; }
        public string Password { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string? Username { get; set; }
        public int? RoleId { get; set; }
        public string? MobileNumber { get; set; }
        public string? WorkPhoneNumber { get; set; }
    }

    public class CreateUserDto
    {
        public int? StatusId { get; set; }
        public int? SalutationId { get; set; }

        public string FirstName { get; set; } = null!;
        public string LastName { get; set; } = null!;
        public string? MobileNumber { get; set; }
        public int? DepartmentId { get; set; }
        public string? WorkEmail { get; set; }
        public string? WorkPhoneNumber { get; set; }
        public string VerificationEmail { get; set; }
    }

    public class UpdateUserDto
    {
        public int? StatusId { get; set; }
        public int? SalutationId { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? MobileNumber { get; set; }
        public int? DepartmentId { get; set; }
        public string? WorkEmail { get; set; }
        public string? WorkPhoneNumber { get; set; }

        public string? VerificationEmail { get; set; }
    }

    public class UpdateRoleDto
    {
        public int RoleId { get; set; }
    }

    public class UpdateCurrentUserDto
    {
        public string FirstName { get; set; } = null!;
        public string LastName { get; set; } = null!;
        public string? WorkPhoneNumber { get; set; }
        public string? MobileNumber { get; set; }
    }

    public class SendUserInviteDto
    {

        public int UserId { get; set; }
    }
}
