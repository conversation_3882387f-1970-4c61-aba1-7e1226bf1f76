﻿using goodkey_cms.DTO;
using goodkey_cms.DTO.Tax;
using goodkey_common.DTO;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class TaxController : Controller
    {
        private readonly ITaxRepository _repo;
        public TaxController(ITaxRepository repo)
        {
            _repo = repo;
        }

        [HttpGet("[action]")]
        public GenericRespond<IEnumerable<BasicDetail>> GetTaxType()
        {
            return new GenericRespond<IEnumerable<BasicDetail>>
            {
                Data = _repo.GetTaxTypes().Select(x => new BasicDetail()
                {
                    Id = x.Id,
                    Name = x.TaxName
                }),
            };
        }

        [HttpGet("[action]")]
        public GenericRespond<IEnumerable<TaxDto>> GetAllTax()
        {
            return new GenericRespond<IEnumerable<TaxDto>>
            {
                Data = _repo.GetTaxProvince().Select(x => new TaxDto()
                {
                    Id = x.Id,
                    TaxType = x.TaxType.TaxName,
                    TaxProvince = x.Province.ProvinceName,
                    TaxRate = x.TaxRate,
                    TaxTypeAbr = x.TaxType.TaxAbr,
                    DisplayOrder = x.DisplayOrder
                })
            };
        }

        [HttpPost("[action]")]
        public GenericRespond<bool> CreateTaxProvince(TaxRequestDto item)
        {
            return new GenericRespond<bool>
            {
                Data = _repo.CreateTaxProvince(item.TaxTypeId, item.ProvinceId, item.DisplayOrder, item.TaxRate)
            };
        }


        [HttpPatch("[action]/{id}")]
        public GenericRespond<bool> UpdateTaxProvince(int id, TaxUpdateDto item)
        {
            return new GenericRespond<bool>
            {
                Data = _repo.UpdateTaxProvince(id, item.DisplayOrder, item.TaxRate)
            };
        }

        [HttpDelete("[action]/{id}")]
        public GenericRespond<bool> DeleteTaxProvince(int id, TaxUpdateDto item)
        {
            return new GenericRespond<bool>
            {
                
            };
        }
    }
}
