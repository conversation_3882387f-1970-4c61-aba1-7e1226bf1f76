﻿namespace goodkey_cms.DTO.User
{
    public class UserData
    {
        public string Email { get; set; }
        public string Password { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string? Username { get; set; }
        public int? RoleId { get; set; }
        public string? MobileNumber { get; set; }
        public string? WorkPhoneNumber { get; set; }
    }

    public class AddUserDto : UpdateUserDto
    {
        public string Email { get; set; }
    }

    public class UpdateUserDto
    {
        public string? Email { get; set; }
        public int? RoleId { get; set; }
    }

    public class UpdateRoleDto
    {
        public int RoleId { get; set; }
    }

    public class UpdateCurrentUserDto
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string? WorkPhoneNumber { get; set; }
        public string? MobileNumber { get; set; }
    }
}
