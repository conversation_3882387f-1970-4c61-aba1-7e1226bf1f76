﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Models
{
    [Table("auth_user")]
    public partial class AuthUser
    {
        public AuthUser()
        {
            InverseArchivedBy = new HashSet<AuthUser>();
            InverseCreatedBy = new HashSet<AuthUser>();
            InverseUpdatedBy = new HashSet<AuthUser>();
        }

        [Key]
        [Column("user_id")]
        public int UserId { get; set; }
        [Column("role_id")]
        public int? RoleId { get; set; }
        [Required]
        [Column("username")]
        [StringLength(255)]
        public string Username { get; set; }
        [Column("password_hash")]
        [StringLength(255)]
        public string PasswordHash { get; set; }
        [Column("status_id")]
        public int? StatusId { get; set; }
        [Column("preferred_lang_id")]
        [StringLength(3)]
        public string PreferredLangId { get; set; }
        [Column("salutation_id")]
        public int? SalutationId { get; set; }
        [Column("first_name")]
        [StringLength(255)]
        public string FirstName { get; set; }
        [Column("last_name")]
        [StringLength(255)]
        public string LastName { get; set; }
        [Column("department_id")]
        public int? DepartmentId { get; set; }
        [Column("company_id")]
        public int? CompanyId { get; set; }
        [Column("work_email")]
        [StringLength(255)]
        public string WorkEmail { get; set; }
        [Column("work_phone_number")]
        [StringLength(15)]
        public string WorkPhoneNumber { get; set; }
        [Column("gender")]
        [StringLength(15)]
        public string Gender { get; set; }
        [Column("dob")]
        public DateOnly? Dob { get; set; }
        [Column("mobile_number")]
        [StringLength(15)]
        public string MobileNumber { get; set; }
        [Column("personal_email")]
        [StringLength(255)]
        public string PersonalEmail { get; set; }
        [Column("address")]
        [StringLength(255)]
        public string Address { get; set; }
        [Column("city")]
        [StringLength(100)]
        public string City { get; set; }
        [Column("province_id")]
        public int? ProvinceId { get; set; }
        [Column("country_id")]
        public int? CountryId { get; set; }
        [Column("postal_code")]
        [StringLength(10)]
        public string PostalCode { get; set; }
        [Column("emergency_contact")]
        [StringLength(100)]
        public string EmergencyContact { get; set; }
        [Column("emergency_phone_number")]
        [StringLength(15)]
        public string EmergencyPhoneNumber { get; set; }
        [Column("is_verified")]
        public bool? IsVerified { get; set; }
        [Column("is_active")]
        public bool? IsActive { get; set; }
        [Column("is_archived")]
        public bool? IsArchived { get; set; }
        [Column("verification_token")]
        [StringLength(255)]
        public string VerificationToken { get; set; }
        [Column("verification_sent_date", TypeName = "timestamp without time zone")]
        public DateTime? VerificationSentDate { get; set; }
        [Column("verification_email")]
        [StringLength(150)]
        public string VerificationEmail { get; set; }
        [Column("created_by_id")]
        public int? CreatedById { get; set; }
        [Column("created_date", TypeName = "timestamp without time zone")]
        public DateTime? CreatedDate { get; set; }
        [Column("updated_by_id")]
        public int? UpdatedById { get; set; }
        [Column("update_date", TypeName = "timestamp without time zone")]
        public DateTime? UpdateDate { get; set; }
        [Column("archived_by_id")]
        public int? ArchivedById { get; set; }
        [Column("archived_date", TypeName = "timestamp without time zone")]
        public DateTime? ArchivedDate { get; set; }

        [ForeignKey("ArchivedById")]
        [InverseProperty("InverseArchivedBy")]
        public virtual AuthUser ArchivedBy { get; set; }
        [ForeignKey("CompanyId")]
        [InverseProperty("AuthUsers")]
        public virtual Company Company { get; set; }
        [ForeignKey("CountryId")]
        [InverseProperty("AuthUsers")]
        public virtual Country Country { get; set; }
        [ForeignKey("CreatedById")]
        [InverseProperty("InverseCreatedBy")]
        public virtual AuthUser CreatedBy { get; set; }
        [ForeignKey("DepartmentId")]
        [InverseProperty("AuthUsers")]
        public virtual Department Department { get; set; }
        [ForeignKey("PreferredLangId")]
        [InverseProperty("AuthUsers")]
        public virtual Language PreferredLang { get; set; }
        [ForeignKey("ProvinceId")]
        [InverseProperty("AuthUsers")]
        public virtual Province Province { get; set; }
        [ForeignKey("RoleId")]
        [InverseProperty("AuthUsers")]
        public virtual AuthRole Role { get; set; }
        [ForeignKey("SalutationId")]
        [InverseProperty("AuthUsers")]
        public virtual Salutation Salutation { get; set; }
        [ForeignKey("StatusId")]
        [InverseProperty("AuthUsers")]
        public virtual AuthStatus Status { get; set; }
        [ForeignKey("UpdatedById")]
        [InverseProperty("InverseUpdatedBy")]
        public virtual AuthUser UpdatedBy { get; set; }
        [InverseProperty("ArchivedBy")]
        public virtual ICollection<AuthUser> InverseArchivedBy { get; set; }
        [InverseProperty("CreatedBy")]
        public virtual ICollection<AuthUser> InverseCreatedBy { get; set; }
        [InverseProperty("UpdatedBy")]
        public virtual ICollection<AuthUser> InverseUpdatedBy { get; set; }
    }
}