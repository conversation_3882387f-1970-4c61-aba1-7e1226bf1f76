﻿
using goodkey_common.Context;
using goodkey_common.Models;

namespace goodkey_cms.Repositories
{
    public interface IPermissionRepository
    {
        IEnumerable<AuthPermission> GetAll();
    }

    public class PermissionRepository : IPermissionRepository
    {
        private readonly GoodkeyContext _context;

        public PermissionRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public IEnumerable<AuthPermission> GetAll()
        {
            return _context.AuthPermission;
        }
    }
}

