﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using goodkey_common.Models;

namespace goodkey_common.Context
{
    public partial class GoodkeyContext : DbContext
    {
        public GoodkeyContext(DbContextOptions<GoodkeyContext> options)
            : base(options)
        {
        }

        public virtual DbSet<AuthPermission> AuthPermissions { get; set; }
        public virtual DbSet<AuthRole> AuthRoles { get; set; }
        public virtual DbSet<AuthStatus> AuthStatuses { get; set; }
        public virtual DbSet<AuthUser> AuthUsers { get; set; }
        public virtual DbSet<Company> Companies { get; set; }
        public virtual DbSet<Country> Countries { get; set; }
        public virtual DbSet<Department> Departments { get; set; }
        public virtual DbSet<Industry> Industries { get; set; }
        public virtual DbSet<Language> Languages { get; set; }
        public virtual DbSet<Menu> Menus { get; set; }
        public virtual DbSet<MenuItem> MenuItems { get; set; }
        public virtual DbSet<MenuSection> MenuSections { get; set; }
        public virtual DbSet<Province> Provinces { get; set; }
        public virtual DbSet<Salutation> Salutations { get; set; }
        public virtual DbSet<ShowLocation> ShowLocations { get; set; }
        public virtual DbSet<ShowLocationContact> ShowLocationContacts { get; set; }
        public virtual DbSet<ShowLocationHall> ShowLocationHalls { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.HasPostgresExtension("dblink");

            modelBuilder.Entity<AuthPermission>(entity =>
            {
                entity.HasKey(e => e.PermissionId)
                    .HasName("auth_permission_pkey");
            });

            modelBuilder.Entity<AuthRole>(entity =>
            {
                entity.HasKey(e => e.RoleId)
                    .HasName("auth_role_pkey");

                entity.HasMany(d => d.Permissions)
                    .WithMany(p => p.Roles)
                    .UsingEntity<Dictionary<string, object>>(
                        "AuthRolePermission",
                        l => l.HasOne<AuthPermission>().WithMany().HasForeignKey("PermissionId").HasConstraintName("fk_role_permission_permission"),
                        r => r.HasOne<AuthRole>().WithMany().HasForeignKey("RoleId").HasConstraintName("fk_role_permission_role"),
                        j =>
                        {
                            j.HasKey("RoleId", "PermissionId").HasName("auth_role_permission_pkey");

                            j.ToTable("auth_role_permission");

                            j.IndexerProperty<int>("RoleId").HasColumnName("role_id");

                            j.IndexerProperty<int>("PermissionId").HasColumnName("permission_id");
                        });
            });

            modelBuilder.Entity<AuthStatus>(entity =>
            {
                entity.HasKey(e => e.StatusId)
                    .HasName("employee_status_pkey");
            });

            modelBuilder.Entity<AuthUser>(entity =>
            {
                entity.HasKey(e => e.UserId)
                    .HasName("auth_user_pkey");

                entity.Property(e => e.IsActive).HasDefaultValueSql("true");

                entity.Property(e => e.IsArchived).HasDefaultValueSql("false");

                entity.Property(e => e.IsVerified).HasDefaultValueSql("false");

                entity.HasOne(d => d.ArchivedBy)
                    .WithMany(p => p.InverseArchivedBy)
                    .HasForeignKey(d => d.ArchivedById)
                    .HasConstraintName("fk_auth_user_archived_by");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.AuthUsers)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_auth_user_company");

                entity.HasOne(d => d.Country)
                    .WithMany(p => p.AuthUsers)
                    .HasForeignKey(d => d.CountryId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_auth_user_country");

                entity.HasOne(d => d.CreatedBy)
                    .WithMany(p => p.InverseCreatedBy)
                    .HasForeignKey(d => d.CreatedById)
                    .HasConstraintName("fk_auth_user_created_by");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.AuthUsers)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("fk_auth_user_department");

                entity.HasOne(d => d.PreferredLang)
                    .WithMany(p => p.AuthUsers)
                    .HasForeignKey(d => d.PreferredLangId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_auth_user_preferred_lang");

                entity.HasOne(d => d.Province)
                    .WithMany(p => p.AuthUsers)
                    .HasForeignKey(d => d.ProvinceId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_auth_user_province");

                entity.HasOne(d => d.Role)
                    .WithMany(p => p.AuthUsers)
                    .HasForeignKey(d => d.RoleId)
                    .HasConstraintName("auth_user_role_id_fkey");

                entity.HasOne(d => d.Salutation)
                    .WithMany(p => p.AuthUsers)
                    .HasForeignKey(d => d.SalutationId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_auth_user_salutation");

                entity.HasOne(d => d.Status)
                    .WithMany(p => p.AuthUsers)
                    .HasForeignKey(d => d.StatusId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_auth_user_status");

                entity.HasOne(d => d.UpdatedBy)
                    .WithMany(p => p.InverseUpdatedBy)
                    .HasForeignKey(d => d.UpdatedById)
                    .HasConstraintName("fk_auth_user_updated_by");
            });

            modelBuilder.Entity<Company>(entity =>
            {
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.HasOne(d => d.Country)
                    .WithMany(p => p.Companies)
                    .HasForeignKey(d => d.CountryId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_country");

                entity.HasOne(d => d.Industry)
                    .WithMany(p => p.Companies)
                    .HasForeignKey(d => d.IndustryId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("company_industry_id_fkey");

                entity.HasOne(d => d.Province)
                    .WithMany(p => p.Companies)
                    .HasForeignKey(d => d.ProvinceId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_province");
            });

            modelBuilder.Entity<Country>(entity =>
            {
                entity.Property(e => e.CountryId).UseIdentityAlwaysColumn();
            });

            modelBuilder.Entity<Department>(entity =>
            {
                entity.Property(e => e.DepartmentId).HasDefaultValueSql("nextval('employee_department_department_id_seq'::regclass)");
            });

            modelBuilder.Entity<Language>(entity =>
            {
                entity.HasKey(e => e.LangCode)
                    .HasName("language_pkey");
            });

            modelBuilder.Entity<Menu>(entity =>
            {
                entity.HasMany(d => d.Roles)
                    .WithMany(p => p.Menus)
                    .UsingEntity<Dictionary<string, object>>(
                        "MenuRole",
                        l => l.HasOne<AuthRole>().WithMany().HasForeignKey("RoleId").HasConstraintName("fk_menu_role_role"),
                        r => r.HasOne<Menu>().WithMany().HasForeignKey("MenuId").HasConstraintName("fk_menu_role_menu"),
                        j =>
                        {
                            j.HasKey("MenuId", "RoleId").HasName("pk_menu_role");

                            j.ToTable("menu_role");

                            j.IndexerProperty<int>("MenuId").HasColumnName("menu_id");

                            j.IndexerProperty<int>("RoleId").HasColumnName("role_id");
                        });
            });

            modelBuilder.Entity<MenuItem>(entity =>
            {
                entity.HasOne(d => d.Menu)
                    .WithMany(p => p.MenuItems)
                    .HasForeignKey(d => d.MenuId)
                    .HasConstraintName("fk_menu_item_menu");

                entity.HasOne(d => d.Parent)
                    .WithMany(p => p.InverseParent)
                    .HasForeignKey(d => d.ParentId)
                    .HasConstraintName("fk_menu_item_parent");

                entity.HasOne(d => d.Section)
                    .WithMany(p => p.MenuItems)
                    .HasForeignKey(d => d.SectionId)
                    .HasConstraintName("fk_menu_item_menu_section");

                entity.HasMany(d => d.Roles)
                    .WithMany(p => p.MenuItems)
                    .UsingEntity<Dictionary<string, object>>(
                        "MenuItemRole",
                        l => l.HasOne<AuthRole>().WithMany().HasForeignKey("RoleId").HasConstraintName("fk_menu_item_role_role"),
                        r => r.HasOne<MenuItem>().WithMany().HasForeignKey("MenuItemId").HasConstraintName("fk_menu_item_role_menu_item"),
                        j =>
                        {
                            j.HasKey("MenuItemId", "RoleId").HasName("menu_item_role_pkey");

                            j.ToTable("menu_item_role");

                            j.IndexerProperty<int>("MenuItemId").HasColumnName("menu_item_id");

                            j.IndexerProperty<int>("RoleId").HasColumnName("role_id");
                        });
            });

            modelBuilder.Entity<Province>(entity =>
            {
                entity.Property(e => e.ProvinceId).UseIdentityAlwaysColumn();

                entity.Property(e => e.DisplayOrder).HasDefaultValueSql("1");

                entity.HasOne(d => d.Country)
                    .WithMany(p => p.Provinces)
                    .HasForeignKey(d => d.CountryId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("Provinces_Country_ID_fkey");
            });

            modelBuilder.Entity<ShowLocation>(entity =>
            {
                entity.HasKey(e => e.LocationId)
                    .HasName("show_locations_pkey");

                entity.HasOne(d => d.LocationCountry)
                    .WithMany(p => p.ShowLocationLocationCountries)
                    .HasForeignKey(d => d.LocationCountryId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_location_country");

                entity.HasOne(d => d.LocationProvince)
                    .WithMany(p => p.ShowLocationLocationProvinces)
                    .HasForeignKey(d => d.LocationProvinceId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_location_province");

                entity.HasOne(d => d.ShippingCountry)
                    .WithMany(p => p.ShowLocationShippingCountries)
                    .HasForeignKey(d => d.ShippingCountryId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_shipping_country");

                entity.HasOne(d => d.ShippingProvince)
                    .WithMany(p => p.ShowLocationShippingProvinces)
                    .HasForeignKey(d => d.ShippingProvinceId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_shipping_province");
            });

            modelBuilder.Entity<ShowLocationContact>(entity =>
            {
                entity.HasKey(e => e.ContactId)
                    .HasName("show_location_contacts_pkey");

                entity.HasOne(d => d.Location)
                    .WithMany(p => p.ShowLocationContacts)
                    .HasForeignKey(d => d.LocationId)
                    .HasConstraintName("fk_location_contact");
            });

            modelBuilder.Entity<ShowLocationHall>(entity =>
            {
                entity.HasKey(e => e.HallId)
                    .HasName("show_location_halls_pkey");

                entity.HasOne(d => d.Location)
                    .WithMany(p => p.ShowLocationHalls)
                    .HasForeignKey(d => d.LocationId)
                    .HasConstraintName("fk_location");
            });

            modelBuilder.HasSequence("Auth_Permission_id_seq");

            modelBuilder.HasSequence("Auth_Role_id_seq");

            modelBuilder.HasSequence("Auth_User_id_seq");

            modelBuilder.HasSequence("Menu_Item_MenuItemID_seq");

            modelBuilder.HasSequence("Menu_Section_Id_seq");

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}