﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_cms.Repositories
{
    public interface IDepartmentRepository
    {
        IEnumerable<Department> GetAll();
        Department? Get(int id);
        bool Add(string name, string username);
        bool Update(int id, string name, string username);
    }

    public class DepartmentRepository : IDepartmentRepository
    {
        private readonly GoodkeyContext _context;

        public DepartmentRepository(GoodkeyContext context)
        {
            _context = context;

        }

        public bool Add(string name, string username)
        {
            AuthUser? user = _context.AuthUser.FirstOrDefault(x => x.Username == username);

            if (user == null)
            {
                return false;
            }

            // Check if a department with the same name already exists
            var existingDepartment = _context.Department.FirstOrDefault(d => d.Name == name);
            if (existingDepartment != null)
            {
                return false;
            }

            var department = new Department()
            {
                Name = name,

            };





            _context.Department.Add(department);
            _context.SaveChanges();
            return true;





        }

        public Department? Get(int id)
        {
            return _context.Department.FirstOrDefault(x => x.DepartmentId == id);
        }

        public IEnumerable<Department> GetAll()
        {
            return _context.Department;
        }

        public bool Update(int id, string name, string username)
        {
            AuthUser? user = _context.AuthUser.FirstOrDefault(x => x.Username == username);
            var department = _context.Department.FirstOrDefault(x => x.DepartmentId == id);

            if (department == null || user == null)
            {
                return false;
            }

            int userId = user.UserId;

            department.Name = name;

            try
            {
                _context.Department.Update(department);
                _context.SaveChanges();
                return true;
            }
            catch (Exception)
            {
                // Log the exception if needed
                return false;
            }
        }
    }
}
