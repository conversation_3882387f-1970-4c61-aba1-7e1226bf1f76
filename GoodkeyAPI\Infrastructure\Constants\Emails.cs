﻿namespace goodkey_cms.Infrastructure.Constants
{
    public class Emails
    {
        public static string VerificationEmail = "<p style=\"\">Dear {{Name}},</p><p style=\"margin-bottom: 20px;\">An account has been created using you email.</p><p style=\"margin-bottom: 20px;\">To verify your account and start using our services, please click the following link:</p><p style=\"margin-bottom: 20px;\"><a href=\"{{VerificationLink}}\" style=\"background-color: #E0201A; color: #ffffff; text-decoration: none; padding: 10px 20px; border-radius: 5px; display: inline-block;\">Verify Account</a></p><p style=\"margin-bottom: 20px;\">If you did not request this registration, please ignore this email.</p><p style=\"margin-bottom: 0;\">Best regards,<br> Malopan</p>";
        public static string ResetPassword = "<p style=\"\">Dear {{Name}},</p><p style=\"margin-bottom: 20px;\">You have requested a password reset.</p><p style=\"margin-bottom: 20px;\">To reset your password, please click the following link:</p><p style=\"margin-bottom: 20px;\"><a href=\"{{VerificationLink}}\" style=\"background-color: #E0201A; color: #ffffff; text-decoration: none; padding: 10px 20px; border-radius: 5px; display: inline-block;\">Verify Account</a></p><p style=\"margin-bottom: 20px;\">If you did not request a password reset, please ignore this email.</p><p style=\"margin-bottom: 0;\">Best regards,<br> Malopan</p>";
        public static string NewAccountCreated = "<p>Hello,</p><p style=\"margin-bottom: 20px;\">Congratulations! Your account has been successfully created.</p><p style=\"margin-bottom: 20px;\">Below are your login credentials:</p><ul style=\"margin-bottom: 20px; list-style-type: none; padding-left: 0;\"><li><strong>Username:</strong> {{Username}}</li></ul><p style=\"margin-bottom: 20px;\">To complete your account setup, please click the link below to create your password:</p><p><a href=\"{{VerificationLink}}\">{{VerificationLink}}</a></p><p style=\"margin-bottom: 0;\">Best regards,<br>The GoodKey Team</p>\r\n";
        public static string EmployeeInvite = "<p>Hello,</p><p style=\"margin-bottom: 20px;\">You have been invited to join the GoodKey team!</p><p style=\"margin-bottom: 20px;\">Please click the link below to set your password and access your account:</p><p style=\"margin-bottom: 20px;\"><a href=\"{{VerificationLink}}\" style=\"background-color: #E0201A; color: #ffffff; text-decoration: none; padding: 10px 20px; border-radius: 5px; display: inline-block;\">Set Your Password</a></p><p style=\"margin-bottom: 0;\">Best regards,<br>The GoodKey Team</p>";
    }
}
