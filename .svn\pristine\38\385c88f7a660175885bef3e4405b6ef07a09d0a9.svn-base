﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class Countries
    {
        public Countries()
        {
            Company = new HashSet<Company>();
            Provinces = new HashSet<Provinces>();
            ShowLocationsCountry = new HashSet<ShowLocations>();
            ShowLocationsShippingCountry = new HashSet<ShowLocations>();
            Warehouse = new HashSet<Warehouse>();
        }

        public int CountryId { get; set; }
        public string CountryName { get; set; }
        public string CountryNameFr { get; set; }
        public string CountryCode { get; set; }

        public virtual ICollection<Company> Company { get; set; }
        public virtual ICollection<Provinces> Provinces { get; set; }
        public virtual ICollection<ShowLocations> ShowLocationsCountry { get; set; }
        public virtual ICollection<ShowLocations> ShowLocationsShippingCountry { get; set; }
        public virtual ICollection<Warehouse> Warehouse { get; set; }
    }
}