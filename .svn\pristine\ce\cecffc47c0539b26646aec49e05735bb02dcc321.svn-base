﻿using goodkey_cms.DTO;
using goodkey_cms.DTO.User;
using goodkey_cms.Infrastructure.Binders;
using goodkey_cms.Repositories;
using goodkey_common.DTO;
using Microsoft.AspNetCore.Mvc;

namespace CN_cms.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class UsersController : ControllerBase
    {
        private readonly IUserRepository _repo;

        public UsersController(IUserRepository repo)
        {
            _repo = repo;
        }



        [HttpGet("[action]")]
        public GenericRespond<BriefUserData?> GetCurrent()
        {
            var username = Request.HttpContext.GetUsername();
            if (string.IsNullOrEmpty(username))
            {
                return new GenericRespond<BriefUserData?>
                {
                    Data = null,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }

            var item = _repo.GetUserByUsername(username);
            if (item == null)
            {
                return new GenericRespond<BriefUserData?>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "User not found"
                };
            }

            var email = item.VerificationEmail ?? string.Empty;

            return new GenericRespond<BriefUserData?>
            {
                Data = new BriefUserData
                {
                    Id = item.UserId,
                    Email = email,
                    IsActive = item.IsActive ?? false,
                    IsVerified = item.IsVerified ?? false,
                    Name = item.FirstName + " " + item.LastName,
                    Username = item.Username,
                    Role = item.Role == null ? null : new() { Id = item.Role.RoleId, Name = item.Role.Name },

                }
            };
        }

        [HttpGet]
        public GenericRespond<IEnumerable<BriefUserData>> GetAll()
        {
            var users = _repo.GetAll().Select(item =>
            {
                var brief = new BriefUserData
                {
                    Id = item.UserId,
                    Username = item.Username,
                    IsActive = item.IsActive ?? false,
                    IsVerified = item.IsVerified ?? false
                };

                // Set name using FirstName and LastName directly from AuthUser
                brief.Name = $"{item.FirstName} {item.LastName}".Trim();
                if (string.IsNullOrWhiteSpace(brief.Name))
                {
                    brief.Name = item.Username; // Fallback to username if name is empty
                }

                // Set email using WorkEmail or PersonalEmail from AuthUser
                brief.Email = item.WorkEmail ?? item.PersonalEmail ?? item.VerificationEmail ?? string.Empty;

                // Set role information if available
                if (item.Role != null)
                {
                    brief.Role = new BasicDetail
                    {
                        Id = item.Role.RoleId,
                        Name = item.Role.Name // Using the direct Name property from AuthRole
                    };
                }
                else
                {
                    brief.Role = new BasicDetail();
                }

                // Set department information if available
                if (item.Department != null)
                {
                    brief.Note = item.Department.Name;
                }

                return brief;
            });

            return new GenericRespond<IEnumerable<BriefUserData>> { Data = users };
        }

        [HttpGet("{id:int}")]
        public GenericRespond<BriefUserData?> GetById(int id)
        {
            var item = _repo.GetById(id);

            if (item == null)
            {
                return new GenericRespond<BriefUserData?>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "User not found"
                };
            }

            var brief = new BriefUserData
            {
                Id = item.UserId,
                Username = item.Username,
                IsActive = item.IsActive ?? false,
                IsVerified = item.IsVerified ?? false
            };

            // Set name using FirstName and LastName directly from AuthUser
            brief.Name = $"{item.FirstName} {item.LastName}".Trim();
            if (string.IsNullOrWhiteSpace(brief.Name))
            {
                brief.Name = item.Username; // Fallback to username if name is empty
            }

            // Set email using WorkEmail or PersonalEmail from AuthUser
            brief.Email = item.WorkEmail ?? item.PersonalEmail ?? item.VerificationEmail ?? string.Empty;

            // Set role information if available
            if (item.Role != null)
            {
                brief.Role = new BasicDetail
                {
                    Id = item.Role.RoleId,
                    Name = item.Role.Name // Using the direct Name property from AuthRole
                };
            }
            else
            {
                brief.Role = new BasicDetail();
            }

            // Set department information if available
            if (item.Department != null)
            {
                brief.Note = item.Department.Name;
            }

            return new GenericRespond<BriefUserData?> { Data = brief };
        }


        [HttpPatch("{id:int}/role")]
        public GenericRespond<bool> UpdateRole(int id, [FromBody] UpdateRoleDto dto)
        {
            var username = Request.HttpContext.GetUsername();
            if (string.IsNullOrEmpty(username))
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }

            // Get the user to check their type
            var user = _repo.GetById(id);
            if (user == null)
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 404,
                    Message = "User not found"
                };
            }

            var success = _repo.SetRole(id, dto.RoleId, username);

            return new GenericRespond<bool>
            {
                Data = success,
                Message = success
                    ? $"User role updated successfully."
                    : $"Failed to update user role.",
                StatusCode = success ? 200 : 400
            };
        }
    }
}
