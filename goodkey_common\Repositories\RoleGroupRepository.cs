using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_cms.Repositories
{
    public interface IRoleGroupRepository
    {
        IEnumerable<AuthGroup> GetAll();
        AuthGroup? Get(int id);
        int? Create(string name, int? minLevel, int? maxLevel);
        bool Update(int id, string name, int? minLevel, int? maxLevel);
        bool SetRoles(int id, IEnumerable<int> roleIds, string username);
        IEnumerable<AuthRole> GetRoles(int id);
        IEnumerable<int> GetConflictingRoles(IEnumerable<int> roleIds, int excludeGroupId);
    }

    public class RoleGroupRepository : IRoleGroupRepository
    {
        private readonly GoodkeyContext _context;

        public RoleGroupRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public int? Create(string name, int? minLevel, int? maxLevel)
        {
            if (_context.AuthGroup.Any(g => g.GroupName == name))
            {
                return null;
            }

            // Set default values if not provided
            int finalMinLevel = minLevel ?? 1;
            int finalMaxLevel = maxLevel ?? finalMinLevel;

            // Validate that minLevel <= maxLevel
            if (finalMinLevel > finalMaxLevel)
            {
                return null;
            }

            // Check for overlapping level ranges with existing groups
            if (HasLevelRangeOverlap(finalMinLevel, finalMaxLevel))
            {
                return null;
            }

            var item = new AuthGroup()
            {
                GroupName = name,
                MinLevel = finalMinLevel,
                MaxLevel = finalMaxLevel
            };

            _context.AuthGroup.Add(item);
            _context.SaveChanges();
            return item.GroupId;
        }

        private bool HasLevelRangeOverlap(int minLevel, int maxLevel, int? excludeGroupId = null)
        {
            var query = _context.AuthGroup.AsQueryable();

            if (excludeGroupId.HasValue)
            {
                query = query.Where(g => g.GroupId != excludeGroupId.Value);
            }

            var existingGroups = query
                .Where(g => g.MinLevel.HasValue && g.MaxLevel.HasValue)
                .Select(g => new { g.MinLevel, g.MaxLevel })
                .ToList();

            foreach (var existingGroup in existingGroups)
            {
                // Check if ranges overlap: (minLevel <= existingMaxLevel && maxLevel >= existingMinLevel)
                if (minLevel <= existingGroup.MaxLevel && maxLevel >= existingGroup.MinLevel)
                {
                    return true;
                }
            }

            return false;
        }

        public AuthGroup? Get(int id)
        {
            return _context.AuthGroup
                .Include(g => g.AuthGroupRole)
                    .ThenInclude(gr => gr.Role)
                        .ThenInclude(r => r.AuthUser)
                .FirstOrDefault(g => g.GroupId == id);
        }

        public IEnumerable<AuthGroup> GetAll()
        {
            return _context.AuthGroup
                .Include(g => g.AuthGroupRole)
                    .ThenInclude(gr => gr.Role)
                        .ThenInclude(r => r.AuthUser)
                .OrderBy(g => g.MinLevel)
                .ThenBy(g => g.GroupName);
        }

        public IEnumerable<AuthRole> GetRoles(int id)
        {
            var group = _context.AuthGroup
                .Include(g => g.AuthGroupRole)
                    .ThenInclude(gr => gr.Role)
                        .ThenInclude(r => r.AuthUser)
                .FirstOrDefault(g => g.GroupId == id);

            return group?.AuthGroupRole.Select(gr => gr.Role) ?? new List<AuthRole>();
        }

        public bool SetRoles(int id, IEnumerable<int> roleIds, string username)
        {
            var group = _context.AuthGroup
                .Include(g => g.AuthGroupRole)
                .FirstOrDefault(g => g.GroupId == id);

            if (group == null)
                return false;

            var user = _context.AuthUser.FirstOrDefault(u => u.Username.ToLower() == username.ToLower());
            if (user == null)
                return false;

            _context.AuthGroupRole.RemoveRange(group.AuthGroupRole);

            foreach (var roleId in roleIds)
            {
                var role = _context.AuthRole.FirstOrDefault(r => r.RoleId == roleId);
                if (role == null)
                    continue;

                var existingGroupRole = _context.AuthGroupRole
                    .FirstOrDefault(agr => agr.RoleId == roleId);

                if (existingGroupRole != null)
                {
                    _context.AuthGroupRole.Remove(existingGroupRole);
                }

                var groupRole = new AuthGroupRole
                {
                    GroupId = group.GroupId,
                    RoleId = role.RoleId,
                    CreatedById = user.UserId,
                    CreatedDate = DateTime.Now
                };

                _context.AuthGroupRole.Add(groupRole);
            }

            _context.SaveChanges();
            return true;
        }

        public IEnumerable<int> GetConflictingRoles(IEnumerable<int> roleIds, int excludeGroupId)
        {
            return _context.AuthGroupRole
                .Where(agr => roleIds.Contains(agr.RoleId) && agr.GroupId != excludeGroupId)
                .Select(agr => agr.RoleId)
                .Distinct()
                .ToList();
        }

        public bool Update(int id, string name, int? minLevel, int? maxLevel)
        {
            var group = _context.AuthGroup.FirstOrDefault(g => g.GroupId == id);
            if (group == null)
                return false;

            if (_context.AuthGroup.Any(g => g.GroupName == name && g.GroupId != id))
            {
                return false;
            }

            // Use existing values if new ones are not provided
            int finalMinLevel = minLevel ?? group.MinLevel ?? 1;
            int finalMaxLevel = maxLevel ?? group.MaxLevel ?? finalMinLevel;

            // Validate that minLevel <= maxLevel
            if (finalMinLevel > finalMaxLevel)
            {
                return false;
            }

            // Check for overlapping level ranges with other existing groups
            if (HasLevelRangeOverlap(finalMinLevel, finalMaxLevel, id))
            {
                return false;
            }

            group.GroupName = name;
            group.MinLevel = finalMinLevel;
            group.MaxLevel = finalMaxLevel;

            _context.SaveChanges();
            return true;
        }


    }
}
