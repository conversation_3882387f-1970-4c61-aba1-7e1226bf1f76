﻿using goodkey_cms.Services;
using goodkey_common.Context;
using Microsoft.AspNetCore.Authorization;

namespace goodkey_cms.Infrastructure.Handlers
{
    public class PermissionRequirement : IAuthorizationRequirement
    {
        public PermissionRequirement(string name)
        {
            Name = name;
        }

        public string Name { get; }
    }
    public class PermissionAuthorizationHandler : IAuthorizationHandler
    {
        private readonly AuthService _authService;
        public PermissionAuthorizationHandler(GoodkeyContext context, AuthService authService)
        {
            _authService = authService;
        }

        public Task HandleAsync(AuthorizationHandlerContext context)
        {
            var pendingRequirements = context.PendingRequirements.OfType<RoleRequirement>().ToList();
            if (!pendingRequirements.Any())
            {
                return Task.CompletedTask;
            }
            var user = _authService.Current;
            if (user == null)
            {
                return Task.CompletedTask;
            }

            var userPermission = user.Role?.Permission.Select(x => x.Name).ToList();

            foreach (var requirement in pendingRequirements)
            {

                if (userPermission.Contains(requirement.Name))
                {
                    context.Succeed(requirement);
                }
            }

            return Task.CompletedTask;
        }


    }
}
