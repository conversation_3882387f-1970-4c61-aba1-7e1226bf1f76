﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Models
{
    [Table("countries")]
    public partial class Country
    {
        public Country()
        {
            AuthUsers = new HashSet<AuthUser>();
            Companies = new HashSet<Company>();
            Provinces = new HashSet<Province>();
            ShowLocationLocationCountries = new HashSet<ShowLocation>();
            ShowLocationShippingCountries = new HashSet<ShowLocation>();
        }

        [Key]
        [Column("Country_ID")]
        public int CountryId { get; set; }
        [Column("Country_Name")]
        public string CountryName { get; set; }
        [Column("Country_Name_Fr")]
        public string CountryNameFr { get; set; }
        [Column("Country_Code")]
        public string CountryCode { get; set; }

        [InverseProperty("Country")]
        public virtual ICollection<AuthUser> AuthUsers { get; set; }
        [InverseProperty("Country")]
        public virtual ICollection<Company> Companies { get; set; }
        [InverseProperty("Country")]
        public virtual ICollection<Province> Provinces { get; set; }
        [InverseProperty("LocationCountry")]
        public virtual ICollection<ShowLocation> ShowLocationLocationCountries { get; set; }
        [InverseProperty("ShippingCountry")]
        public virtual ICollection<ShowLocation> ShowLocationShippingCountries { get; set; }
    }
}