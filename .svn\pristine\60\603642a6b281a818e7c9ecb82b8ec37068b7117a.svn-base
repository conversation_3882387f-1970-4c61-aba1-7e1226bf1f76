﻿using goodkey_cms.DTO;
using goodkey_cms.DTO.Warehouse;
using goodkey_cms.Services;
using goodkey_common.DTO;
using goodkey_common.Models;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class WarehouseController : ControllerBase
    {
        private readonly IWarehouseRepository _warehouseRepository;
        private readonly AuthService _service;

        public WarehouseController(IWarehouseRepository warehouseRepository, AuthService service)
        {
            _warehouseRepository = warehouseRepository;
            _service = service;
        }

        [HttpGet("[action]")]
        public GenericRespond<IEnumerable<BasicDetail>> GetAllType()
        {
            var warehouseTypes = _warehouseRepository.GetAll()
                .Select(w => new BasicDetail
                {
                    Id = w.WarehouseTypeId,
                    Name = $"{w.Code} - {w.TypeName}"
                });

            return new GenericRespond<IEnumerable<BasicDetail>>
            {
                Data = warehouseTypes
            };
        }

        [HttpGet]
        public async Task<GenericRespond<IEnumerable<WarehouseSummaryDto>>> GetAll([FromQuery] int? warehouseTypeId)
        {
            var warehouses = await _warehouseRepository.GetAllAsync();
            if (warehouseTypeId.HasValue)
            {
                warehouses = warehouses.Where(w => w.WarehouseTypeId == warehouseTypeId).ToList();
            }

            var dtos = warehouses.Select(w => new WarehouseSummaryDto
            {
                Id = w.WarehouseId,
                Code = w.Code,
                WarehouseName = w.WarehouseName,
                AddressLine1 = w.AddressLine1,
                AddressLine2 = w.AddressLine2,
                City = w.City,
                PostalCode = w.PostalCode,
                ProvinceName = w.Province?.ProvinceName ?? "",
                CountryName = w.Country?.CountryName ?? "",
                WarehouseTypeName = w.WarehouseType?.TypeName ?? "",
                Phone = w.Phone,
                IsActive = w.IsActive,
                ContactPersonName = w.ContactPerson != null
                    ? $"{w.ContactPerson.FirstName} {w.ContactPerson.LastName}"
                    : ""
            }).ToList();

            return new GenericRespond<IEnumerable<WarehouseSummaryDto>>
            {
                Data = dtos,
                StatusCode = 200,
                Message = "Warehouses retrieved successfully."
            };
        }

        // GET: api/warehouse/5
        [HttpGet("{id}")]
        public async Task<GenericRespond<WarehouseDetailDto>> GetById(int id)
        {
            var warehouse = await _warehouseRepository.GetByIdAsync(id);

            if (warehouse == null)
            {
                return new GenericRespond<WarehouseDetailDto>
                {
                    Data = null,
                    Message = "Warehouse not found",
                    StatusCode = 404
                };
            }

            var dto = new WarehouseDetailDto
            {
                WarehouseId = warehouse.WarehouseId,
                Code = warehouse.Code,
                WarehouseName = warehouse.WarehouseName,
                AddressLine1 = warehouse.AddressLine1,
                AddressLine2 = warehouse.AddressLine2,
                City = warehouse.City,
                PostalCode = warehouse.PostalCode,
                ProvinceId = warehouse.ProvinceId,
                CountryId = warehouse.CountryId,
                Phone = warehouse.Phone,
                WarehouseTypeId = warehouse.WarehouseTypeId,
                ContactPersonId = warehouse.ContactPersonId,
                CreatedAt = warehouse.CreatedAt,
                CreatedById = warehouse.CreatedById,
                UpdatedAt = warehouse.UpdatedAt,
                UpdatedById = warehouse.UpdatedById,
                IsActive = warehouse.IsActive,

            };

            return new GenericRespond<WarehouseDetailDto>
            {
                Data = dto,
                StatusCode = 200,
                Message = "Warehouse retrieved successfully."
            };
        }

        // POST: api/warehouse
        [HttpPost]
        public async Task<GenericRespond<bool>> Create([FromBody] WarehouseCreateUpdateDto dto)
        {
            var user = _service.Current;
            if (user == null)
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    Message = "Unauthorized",
                    StatusCode = 401
                };
            }

            var warehouse = new Warehouse
            {
                Code = string.IsNullOrWhiteSpace(dto.Code) ? GenerateUniqueCode() : dto.Code,
                WarehouseName = dto.WarehouseName,
                AddressLine1 = dto.AddressLine1,
                AddressLine2 = dto.AddressLine2,
                City = dto.City,
                PostalCode = dto.PostalCode,
                ProvinceId = dto.ProvinceId,
                CountryId = dto.CountryId,
                WarehouseTypeId = dto.WarehouseTypeId,
                ContactPersonId = string.IsNullOrWhiteSpace(dto.ContactPersonId.ToString()) ? null : dto.ContactPersonId,
                Phone = dto.Phone,
                CreatedAt = DateTime.Now,
                CreatedById = user.UserId,
            };

            // Update activation if changed
            if (dto.IsActive == true)
            {
                warehouse.IsActive = dto.IsActive;
                warehouse.LastUpdateActiveById = user.UserId;
                warehouse.LastUpdatedActiveAt = DateTime.Now;
            }


            await _warehouseRepository.AddAsync(warehouse);

            return new GenericRespond<bool>
            {
                Data = true,
                StatusCode = 201,
                Message = "Warehouse created successfully."
            };
        }

        // PUT: api/warehouse/5
        [HttpPatch("{id}")]
        public async Task<GenericRespond<bool>> Update(int id, [FromBody] WarehouseCreateUpdateDto dto)
        {
            var user = _service.Current;
            if (user == null)
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    Message = "Unauthorized",
                    StatusCode = 401
                };
            }

            var warehouse = await _warehouseRepository.GetByIdAsync(id);
            if (warehouse == null)
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    Message = "Warehouse not found",
                    StatusCode = 404
                };
            }

            warehouse.Code = dto.Code;
            warehouse.WarehouseName = dto.WarehouseName;
            warehouse.AddressLine1 = dto.AddressLine1;
            warehouse.AddressLine2 = dto.AddressLine2;
            warehouse.City = dto.City;
            warehouse.PostalCode = dto.PostalCode;
            warehouse.ProvinceId = dto.ProvinceId;
            warehouse.CountryId = dto.CountryId;
            warehouse.WarehouseTypeId = dto.WarehouseTypeId;
            warehouse.ContactPersonId = string.IsNullOrWhiteSpace(dto.ContactPersonId.ToString()) ? null : dto.ContactPersonId;
            warehouse.Phone = dto.Phone;
            warehouse.UpdatedAt = DateTime.Now;
            warehouse.UpdatedById = user.UserId;

            // Update activation if changed
            if (warehouse.IsActive != dto.IsActive)
            {
                warehouse.IsActive = dto.IsActive;
                warehouse.LastUpdateActiveById = user.UserId;
                warehouse.LastUpdatedActiveAt = DateTime.Now;
            }

            await _warehouseRepository.UpdateAsync(warehouse);

            return new GenericRespond<bool>
            {
                Data = true,
                Message = "Warehouse updated successfully."
            };
        }

        private string GenerateUniqueCode()
        {
            string code;
            do
            {
                code = GenerateRandomCode();
            } while (_warehouseRepository.GetAll().Any(w => w.Code == code));

            return code;
        }

        private string GenerateRandomCode()
        {
            var random = new Random();
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            return new string(Enumerable.Repeat(chars, 3)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }

    }
}
