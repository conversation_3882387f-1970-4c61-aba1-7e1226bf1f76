<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FileSignatures" Version="4.4.1" />
    <PackageReference Include="Handlebars.Net" Version="2.1.4" />
    <PackageReference Include="MailKit" Version="4.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="7.0.20" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="7.0.20" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="7.0.20" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Analyzers" Version="7.0.20" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="7.0.20" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="7.0.20" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="7.0.20">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="MimeKit" Version="4.4.0" />
    <PackageReference Include="MimeTypesMap" Version="1.0.8" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="6.0.29" />
    <PackageReference Include="PdfiumViewer" Version="2.13.0" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.4" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="System.Drawing.Common" Version="7.0.0" />
  </ItemGroup>

</Project>
