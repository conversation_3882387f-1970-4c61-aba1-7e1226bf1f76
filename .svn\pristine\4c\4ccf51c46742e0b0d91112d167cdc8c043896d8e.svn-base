﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class AuthRole
    {
        public AuthRole()
        {
            AuthGroupRole = new HashSet<AuthGroupRole>();
            AuthUser = new HashSet<AuthUser>();
            Menu = new HashSet<Menu>();
            MenuItem = new HashSet<MenuItem>();
            Permission = new HashSet<AuthPermission>();
        }

        public int RoleId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }

        public virtual ICollection<AuthGroupRole> AuthGroupRole { get; set; }
        public virtual ICollection<AuthUser> AuthUser { get; set; }

        public virtual ICollection<Menu> Menu { get; set; }
        public virtual ICollection<MenuItem> MenuItem { get; set; }
        public virtual ICollection<AuthPermission> Permission { get; set; }
    }
}