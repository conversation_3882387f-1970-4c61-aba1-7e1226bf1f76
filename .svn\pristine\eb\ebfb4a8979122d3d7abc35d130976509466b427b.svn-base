﻿using goodkey_cms.DTO;
using goodkey_cms.DTO.Tax;
using goodkey_cms.Services;
using goodkey_common.DTO;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class TaxController : Controller
    {
        private readonly ITaxRepository _repo;
        private readonly AuthService _service;
        public TaxController(ITaxRepository repo, AuthService service)
        {
            _repo = repo;
            _service = service;
        }

        [HttpGet("[action]")]
        public GenericRespond<IEnumerable<BasicDetail>> GetTaxType()
        {
            return new GenericRespond<IEnumerable<BasicDetail>>
            {
                Data = _repo.GetTaxTypes().Select(x => new BasicDetail()
                {
                    Id = x.Id,
                    Name = x.TaxName
                }),
            };
        }

        [HttpGet("[action]")]
        public GenericRespond<IEnumerable<TaxDto>> GetAllTax()
        {
            return new GenericRespond<IEnumerable<TaxDto>>
            {
                Data = _repo.GetTaxProvince().Select(x => new TaxDto()
                {
                    Id = x.Id,
                    TaxType = x.TaxType.TaxName,
                    TaxProvince = x.Province.ProvinceName,
                    TaxRate = x.TaxRate,
                    TaxTypeAbr = x.TaxType.TaxAbr,
                    DisplayOrder = x.DisplayOrder
                })
            };
        }

        [HttpPost("[action]")]
        public GenericRespond<bool> CreateTaxProvince(TaxRequestDto item)
        {
            var user = _service.Current;
            return new GenericRespond<bool>
            {
                Data = _repo.CreateTaxProvince(item.TaxTypeId, item.ProvinceId, item.DisplayOrder, item.TaxRate, user.UserId)
            };
        }


        [HttpPatch("[action]/{id}")]
        public GenericRespond<bool> UpdateTaxProvince(int id, TaxUpdateDto item)
        {
            var user = _service.Current;
            return new GenericRespond<bool>
            {
                Data = _repo.UpdateTaxProvince(id, item.DisplayOrder, item.TaxRate, user.UserId)
            };
        }

        [HttpPatch("[action]/{id}")]
        public ActionResult<GenericRespond<bool>> UpdateIsActive(int id, [FromQuery] bool isActive)
        {
            var user = _service.Current;

            var result = _repo.UpdateIsActive(id, isActive, user.UserId);

            return new GenericRespond<bool>
            {
                Data = result
            };
        }

        [HttpGet("[action]/{id}")]
        public ActionResult<GenericRespond<TaxDto>> GetTax(int id)
        {
            var result = _repo.GetTaxProvince(id);

            return new GenericRespond<TaxDto>
            {
                Data = new TaxDto()
                {
                    Id = id,
                    TaxProvince = result.Province.ProvinceName,
                    TaxRate = result.TaxRate,
                    TaxType = result.TaxType.TaxName,
                    TaxTypeAbr = result.TaxType.TaxAbr,
                    DisplayOrder = result.DisplayOrder,
                }
            };
        }

    }
}
