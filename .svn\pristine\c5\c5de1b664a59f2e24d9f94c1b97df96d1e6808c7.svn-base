﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Models
{
    [Table("menu")]
    public partial class Menu
    {
        public Menu()
        {
            MenuItems = new HashSet<MenuItem>();
            Roles = new HashSet<AuthRole>();
        }

        [Key]
        [Column("id")]
        public int Id { get; set; }

        [InverseProperty("Menu")]
        public virtual ICollection<MenuItem> MenuItems { get; set; }

        [ForeignKey("MenuId")]
        [InverseProperty("Menus")]
        public virtual ICollection<AuthRole> Roles { get; set; }
    }
}