﻿
using goodkey_cms.Services;
using goodkey_common.Context;
using Microsoft.AspNetCore.Authorization;

namespace goodkey_cms.Infrastructure.Handlers
{
    public class RoleRequirement : IAuthorizationRequirement
    {
        public RoleRequirement(string role)
        {
            Name = role;
        }

        public string Name { get; }
    }
    public class RoleAuthorizationHandler : IAuthorizationHandler
    {
        private readonly AuthService _authService;
        public RoleAuthorizationHandler(GoodkeyContext context, AuthService authService)
        {
            _authService = authService;
        }

        public Task HandleAsync(AuthorizationHandlerContext context)
        {
            var pendingRequirements = context.PendingRequirements.OfType<RoleRequirement>().ToList();
            if (!pendingRequirements.Any())
            {
                return Task.CompletedTask;
            }
            var user = _authService.Current;
            if (user == null)
            {
                return Task.CompletedTask;
            }            

            foreach (var requirement in pendingRequirements)
            {

                if (user.Role.Name.Equals(requirement.Name))
                {
                    context.Succeed(requirement);
                }
            }

            return Task.CompletedTask;
        }


    }
}
