﻿using System;
using System.Collections.Generic;
using System.Linq;
using goodkey_common.Context;
using goodkey_common.Models;

namespace goodkey_common.Repositories
{
    public interface IWarehouseTypeRepository
    {
        IEnumerable<WarehouseTypes> GetAll();       
    }

    public class WarehouseTypeRepository : IWarehouseTypeRepository
    {
        private readonly GoodkeyContext _context;

        public WarehouseTypeRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public IEnumerable<WarehouseTypes> GetAll()
        {
            return _context.WarehouseTypes.ToList();
        }
    }
}
