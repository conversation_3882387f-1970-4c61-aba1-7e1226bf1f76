﻿using Microsoft.Extensions.DependencyInjection;
using System.Reflection;

public static class RepositoryServiceExtensions
{
    public static IServiceCollection AddRepositories(this IServiceCollection services)
    {
        var assembly = Assembly.GetAssembly(typeof(RepositoryServiceExtensions));

        var repositoryTypes = assembly.GetTypes()
            .Where(type =>
                type.Name.EndsWith("Repository", StringComparison.OrdinalIgnoreCase) &&
                type.GetInterfaces().Any(i => i.Name == "I" + type.Name));

        foreach (var repositoryType in repositoryTypes)
        {
            var interfaces = repositoryType.GetInterfaces();
            foreach (var @interface in interfaces)
            {
                services.AddScoped(@interface, repositoryType);
            }
        }

        return services;
    }
}


