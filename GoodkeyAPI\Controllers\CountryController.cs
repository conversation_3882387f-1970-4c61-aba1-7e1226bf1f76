﻿using goodkey_cms.DTO;
using goodkey_common.DTO;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_dashboard.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class CountriesController : ControllerBase
    {
        private readonly ICountryRepository _repo;
        public CountriesController(ICountryRepository repo)
        {
            _repo = repo;
        }
        [HttpGet("[action]")]

        public GenericRespond<IEnumerable<BasicDetail>> GetBrief()
        {
            return new()
            {
                Data = _repo.GetAll().Select(item =>

                new BasicDetail()
                {
                    Id = item.CountryId,
                    Name = item.CountryName
                }
            )
            };
        }
    }
}
