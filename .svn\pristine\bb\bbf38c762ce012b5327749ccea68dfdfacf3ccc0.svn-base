﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Models
{
    [Table("department")]
    public partial class Department
    {
        public Department()
        {
            AuthUsers = new HashSet<AuthUser>();
        }

        [Key]
        [Column("department_id")]
        public int DepartmentId { get; set; }
        [Column("name")]
        [StringLength(150)]
        public string Name { get; set; }

        [InverseProperty("Department")]
        public virtual ICollection<AuthUser> AuthUsers { get; set; }
    }
}