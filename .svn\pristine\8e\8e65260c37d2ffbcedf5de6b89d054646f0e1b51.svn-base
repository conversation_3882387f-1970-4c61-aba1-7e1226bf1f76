﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Models
{
    [Table("auth_role")]
    public partial class AuthRole
    {
        public AuthRole()
        {
            AuthUsers = new HashSet<AuthUser>();
            MenuItems = new HashSet<MenuItem>();
            Menus = new HashSet<Menu>();
            Permissions = new HashSet<AuthPermission>();
        }

        [Key]
        [Column("role_id")]
        public int RoleId { get; set; }
        [Column("name")]
        [StringLength(150)]
        public string Name { get; set; }
        [Column("description")]
        [StringLength(300)]
        public string Description { get; set; }

        [InverseProperty("Role")]
        public virtual ICollection<AuthUser> AuthUsers { get; set; }

        [ForeignKey("RoleId")]
        [InverseProperty("Roles")]
        public virtual ICollection<MenuItem> MenuItems { get; set; }
        [ForeignKey("RoleId")]
        [InverseProperty("Roles")]
        public virtual ICollection<Menu> Menus { get; set; }
        [ForeignKey("RoleId")]
        [InverseProperty("Roles")]
        public virtual ICollection<AuthPermission> Permissions { get; set; }
    }
}