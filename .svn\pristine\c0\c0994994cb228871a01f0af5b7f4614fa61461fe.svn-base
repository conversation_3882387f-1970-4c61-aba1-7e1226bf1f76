﻿namespace goodkey_cms.DTO.User
{
    public class UserDetail : BasicDetail
    {
        public string Email { get; set; }
        public bool? IsSuper { get; set; }
        public BasicDetail? Role { get; set; }
        public IEnumerable<string> Permission { get; set; }
        public string? Username { get; set; }
        public string? Name { get; set; }
        public IEnumerable<int> MenuItems { get; set; }

    }
    public class BriefUserData : BasicDetail
    {

        public string Username { get; set; }

        public string Email { get; set; }
        public BasicDetail? Role { get; set; }
        public bool IsActive { get; set; }
        public string? Note { get; set; }
        public bool IsVerified { get; set; }
        public string? WorkPhoneNumber { get; set; }
        public string? MobileNumber { get; set; }
    }

    public class EmployeeDetail
    {
        public int Id { get; set; }
        public int? StatusId { get; set; }
        public string? PreferredLang { get; set; }
        public int? SalutationId { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Gender { get; set; }
        public DateTime? Dob { get; set; }
        public string? MobileNumber { get; set; }
        public string? PersonalEmail { get; set; }
        public string? Address { get; set; }
        public string? City { get; set; }
        public string? Province { get; set; }
        public string? PostalCode { get; set; }
        public string? EmergencyContact { get; set; }
        public string? EmergencyPhoneNumber { get; set; }
        public string? EmployeeNumber { get; set; }
        public int? DepartmentId { get; set; }
        public string? WorkEmail { get; set; }
        public string? WorkPhoneNumber { get; set; }
        public bool? Archive { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdateDate { get; set; }
        public bool HasAccount { get; set; } = false;
    }
}
