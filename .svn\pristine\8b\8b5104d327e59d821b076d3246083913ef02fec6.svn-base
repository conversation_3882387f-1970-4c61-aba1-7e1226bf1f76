using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_cms.Repositories
{
    public interface IRoleGroupRepository
    {
        IEnumerable<AuthGroup> GetAll();
        AuthGroup? Get(int id);
        int? Create(string name, int? level);
        bool Update(int id, string name, int? level);
        bool SetRoles(int id, IEnumerable<int> roleIds, string username);
        IEnumerable<AuthRole> GetRoles(int id);
    }

    public class RoleGroupRepository : IRoleGroupRepository
    {
        private readonly GoodkeyContext _context;

        public RoleGroupRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public int? Create(string name, int? level)
        {
            if (_context.AuthGroup.Any(g => g.GroupName == name))
            {
                return null;
            }

            int finalLevel = level ?? GetNextAvailableLevel();

            if (_context.AuthGroup.Any(g => g.Level == finalLevel))
            {
                return null;
            }

            if (HasLevelRangeConflict(finalLevel))
            {
                return null;
            }

            var item = new AuthGroup()
            {
                GroupName = name,
                Level = finalLevel
            };

            _context.AuthGroup.Add(item);
            _context.SaveChanges();
            return item.GroupId;
        }

        private int GetNextAvailableLevel()
        {
            var existingLevels = _context.AuthGroup
                .Select(g => g.Level ?? 1)
                .Where(l => l > 0)
                .OrderBy(l => l)
                .ToList();

            int candidateLevel = 1;
            while (existingLevels.Contains(candidateLevel) || HasLevelRangeConflict(candidateLevel))
            {
                candidateLevel++;
            }

            return candidateLevel;
        }

        private bool HasLevelRangeConflict(int level)
        {
            var existingLevels = _context.AuthGroup
                .Select(g => g.Level ?? 1)
                .Where(l => l > 0)
                .ToList();

            int newLevelRangeStart = ((level - 1) / 10) * 10 + 1;
            int newLevelRangeEnd = newLevelRangeStart + 9;

            foreach (var existingLevel in existingLevels)
            {
                int existingRangeStart = ((existingLevel - 1) / 10) * 10 + 1;
                int existingRangeEnd = existingRangeStart + 9;

                if (newLevelRangeStart <= existingRangeEnd && newLevelRangeEnd >= existingRangeStart)
                {
                    return true;
                }
            }

            return false;
        }

        public AuthGroup? Get(int id)
        {
            return _context.AuthGroup
                .Include(g => g.AuthGroupRole)
                    .ThenInclude(gr => gr.Role)
                .FirstOrDefault(g => g.GroupId == id);
        }

        public IEnumerable<AuthGroup> GetAll()
        {
            return _context.AuthGroup
                .OrderBy(g => g.Level)
                .ThenBy(g => g.GroupName);
        }

        public IEnumerable<AuthRole> GetRoles(int id)
        {
            var group = _context.AuthGroup
                .Include(g => g.AuthGroupRole)
                    .ThenInclude(gr => gr.Role)
                .FirstOrDefault(g => g.GroupId == id);

            return group?.AuthGroupRole.Select(gr => gr.Role) ?? new List<AuthRole>();
        }

        public bool SetRoles(int id, IEnumerable<int> roleIds, string username)
        {
            var group = _context.AuthGroup
                .Include(g => g.AuthGroupRole)
                .FirstOrDefault(g => g.GroupId == id);

            if (group == null)
                return false;

            var user = _context.AuthUser.FirstOrDefault(u => u.Username.ToLower() == username.ToLower());
            if (user == null)
                return false;

            _context.AuthGroupRole.RemoveRange(group.AuthGroupRole);

            foreach (var roleId in roleIds)
            {
                var role = _context.AuthRole.FirstOrDefault(r => r.RoleId == roleId);
                if (role == null)
                    continue;

                var groupRole = new AuthGroupRole
                {
                    GroupId = group.GroupId,
                    RoleId = role.RoleId,
                    CreatedById = user.UserId,
                    CreatedDate = DateTime.Now
                };

                _context.AuthGroupRole.Add(groupRole);
            }

            _context.SaveChanges();
            return true;
        }

        public bool Update(int id, string name, int? level)
        {
            var group = _context.AuthGroup.FirstOrDefault(g => g.GroupId == id);
            if (group == null)
                return false;

            if (_context.AuthGroup.Any(g => g.GroupName == name && g.GroupId != id))
            {
                return false;
            }

            if (level.HasValue)
            {
                if (_context.AuthGroup.Any(g => g.Level == level.Value && g.GroupId != id))
                {
                    return false;
                }

                if (HasLevelRangeConflictForUpdate(level.Value, id))
                {
                    return false;
                }
            }

            group.GroupName = name;
            if (level.HasValue)
            {
                group.Level = level.Value;
            }

            _context.SaveChanges();
            return true;
        }

        private bool HasLevelRangeConflictForUpdate(int level, int excludeGroupId)
        {
            var existingLevels = _context.AuthGroup
                .Where(g => g.GroupId != excludeGroupId)
                .Select(g => g.Level ?? 1)
                .Where(l => l > 0)
                .ToList();

            int newLevelRangeStart = ((level - 1) / 10) * 10 + 1;
            int newLevelRangeEnd = newLevelRangeStart + 9;

            foreach (var existingLevel in existingLevels)
            {
                int existingRangeStart = ((existingLevel - 1) / 10) * 10 + 1;
                int existingRangeEnd = existingRangeStart + 9;

                if (newLevelRangeStart <= existingRangeEnd && newLevelRangeEnd >= existingRangeStart)
                {
                    return true;
                }
            }

            return false;
        }
    }
}
