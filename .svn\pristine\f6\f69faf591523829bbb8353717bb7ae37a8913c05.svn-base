﻿namespace goodkey_cms.DTO.User
{
    public class UserDetail : BasicDetail
    {
        public string Email { get; set; }
        public bool? IsSuper { get; set; }
        public BasicDetail? Role { get; set; }
        public IEnumerable<string> Permission { get; set; }
        public string? Username { get; set; }
        public string? Name { get; set; }

        public IEnumerable<int> MenuItem { get; set; }

    }
    public class BriefUserData : BasicDetail
    {

        public string Username { get; set; }

        public string Email { get; set; }
        public BasicDetail? Role { get; set; }
        public bool IsActive { get; set; }
        public string? Note { get; set; }
        public bool IsVerified { get; set; }
    }
}
