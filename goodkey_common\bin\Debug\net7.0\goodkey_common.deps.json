{"runtimeTarget": {"name": ".NETCoreApp,Version=v7.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v7.0": {"goodkey_common/1.0.0": {"dependencies": {"FileSignatures": "4.4.1", "Handlebars.Net": "2.1.4", "MailKit": "4.3.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "7.0.20", "Microsoft.AspNetCore.OpenApi": "7.0.20", "Microsoft.EntityFrameworkCore": "7.0.20", "Microsoft.EntityFrameworkCore.Analyzers": "7.0.20", "Microsoft.EntityFrameworkCore.Relational": "7.0.20", "Microsoft.EntityFrameworkCore.SqlServer": "7.0.20", "Microsoft.EntityFrameworkCore.Tools": "7.0.20", "MimeKit": "4.4.0", "MimeTypesMap": "1.0.8", "Newtonsoft.Json": "13.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "6.0.29", "PdfiumViewer": "2.13.0", "SixLabors.ImageSharp": "3.1.4", "Swashbuckle.AspNetCore": "6.5.0", "System.Drawing.Common": "7.0.0"}, "runtime": {"goodkey_common.dll": {}}}, "Azure.Core/1.35.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.35.0.0", "fileVersion": "1.3500.23.45706"}}}, "Azure.Identity/1.10.3": {"dependencies": {"Azure.Core": "1.35.0", "Microsoft.Identity.Client": "4.56.0", "Microsoft.Identity.Client.Extensions.Msal": "4.56.0", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "6.0.0", "System.Text.Json": "7.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.10.3.0", "fileVersion": "1.1000.323.51804"}}}, "BouncyCastle.Cryptography/2.3.0": {"runtime": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.3.0.53016"}}}, "FileSignatures/4.4.1": {"dependencies": {"OpenMcdf": "*******", "System.Reflection.TypeExtensions": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard2.0/FileSignatures.dll": {"assemblyVersion": "4.4.1.0", "fileVersion": "4.4.1.0"}}}, "Handlebars.Net/2.1.4": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.1/Handlebars.dll": {"assemblyVersion": "2.1.4.0", "fileVersion": "2.1.4.0"}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "MailKit/4.3.0": {"dependencies": {"MimeKit": "4.4.0"}, "runtime": {"lib/net6.0/MailKit.dll": {"assemblyVersion": "4.3.0.0", "fileVersion": "4.3.0.0"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/7.0.20": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.35.0"}, "runtime": {"lib/net7.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "********", "fileVersion": "7.0.2024.26907"}}}, "Microsoft.AspNetCore.OpenApi/7.0.20": {"dependencies": {"Microsoft.OpenApi": "1.4.3"}, "runtime": {"lib/net7.0/Microsoft.AspNetCore.OpenApi.dll": {"assemblyVersion": "********", "fileVersion": "7.0.2024.26907"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "4.700.20.21406"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Data.SqlClient/5.1.5": {"dependencies": {"Azure.Identity": "1.10.3", "Microsoft.Data.SqlClient.SNI.runtime": "5.1.1", "Microsoft.Identity.Client": "4.56.0", "Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.35.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "6.0.1", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Runtime.Caching": "6.0.0", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "8.0.0", "System.Text.Encodings.Web": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "5.15.24027.2"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.15.24027.2"}, "runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.15.24027.2"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.1": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.EntityFrameworkCore/7.0.20": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "7.0.20", "Microsoft.EntityFrameworkCore.Analyzers": "7.0.20", "Microsoft.Extensions.Caching.Memory": "7.0.0", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "********", "fileVersion": "7.0.2024.26903"}}}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.20": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "7.0.2024.26903"}}}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.20": {}, "Microsoft.EntityFrameworkCore.Design/7.0.20": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.EntityFrameworkCore.Relational": "7.0.20", "Microsoft.Extensions.DependencyModel": "7.0.0", "Mono.TextTemplating": "2.2.1"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "********", "fileVersion": "7.0.2024.26903"}}}, "Microsoft.EntityFrameworkCore.Relational/7.0.20": {"dependencies": {"Microsoft.EntityFrameworkCore": "7.0.20", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "********", "fileVersion": "7.0.2024.26903"}}}, "Microsoft.EntityFrameworkCore.SqlServer/7.0.20": {"dependencies": {"Microsoft.Data.SqlClient": "5.1.5", "Microsoft.EntityFrameworkCore.Relational": "7.0.20"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"assemblyVersion": "********", "fileVersion": "7.0.2024.26903"}}}, "Microsoft.EntityFrameworkCore.Tools/7.0.20": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "7.0.20"}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {}, "Microsoft.Extensions.DependencyModel/7.0.0": {"dependencies": {"System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {}, "Microsoft.Extensions.Options/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Primitives/7.0.0": {}, "Microsoft.Identity.Client/4.56.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.56.0.0", "fileVersion": "4.56.0.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.56.0": {"dependencies": {"Microsoft.Identity.Client": "4.56.0", "System.IO.FileSystem.AccessControl": "5.0.0", "System.Security.Cryptography.ProtectedData": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.56.0.0", "fileVersion": "4.56.0.0"}}}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.35.0", "System.Text.Encoding": "4.3.0", "System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Logging/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Protocols/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.35.0", "System.IdentityModel.Tokens.Jwt": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Tokens/6.35.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.IdentityModel.Logging": "6.35.0", "System.Security.Cryptography.Cng": "5.0.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.OpenApi/1.4.3": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.4.3.0", "fileVersion": "1.4.3.0"}}}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Microsoft.Win32.SystemEvents/7.0.0": {"runtime": {"lib/net7.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "MimeKit/4.4.0": {"dependencies": {"BouncyCastle.Cryptography": "2.3.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Security.Cryptography.Pkcs": "8.0.0", "System.Text.Encoding.CodePages": "8.0.0"}, "runtime": {"lib/net6.0/MimeKit.dll": {"assemblyVersion": "4.4.0.0", "fileVersion": "4.4.0.0"}}}, "MimeTypesMap/1.0.8": {"runtime": {"lib/netstandard2.0/MimeTypesMap.dll": {"assemblyVersion": "1.0.8.0", "fileVersion": "1.0.8.0"}}}, "Mono.TextTemplating/2.2.1": {"dependencies": {"System.CodeDom": "4.4.0"}, "runtime": {"lib/netstandard2.0/Mono.TextTemplating.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.1.1"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.3.27908"}}}, "Npgsql/6.0.11": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Npgsql.dll": {"assemblyVersion": "6.0.11.0", "fileVersion": "6.0.11.0"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/6.0.29": {"dependencies": {"Microsoft.EntityFrameworkCore": "7.0.20", "Microsoft.EntityFrameworkCore.Abstractions": "7.0.20", "Microsoft.EntityFrameworkCore.Relational": "7.0.20", "Npgsql": "6.0.11"}, "runtime": {"lib/net6.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "6.0.29.0", "fileVersion": "6.0.29.0"}}}, "OpenMcdf/*******": {"runtime": {"lib/netstandard2.0/OpenMcdf.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "PdfiumViewer/2.13.0": {"runtime": {"lib/net20/PdfiumViewer.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "resources": {"lib/net20/nl/PdfiumViewer.resources.dll": {"locale": "nl"}}}, "SixLabors.ImageSharp/3.1.4": {"runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore/6.5.0": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.5.0", "Swashbuckle.AspNetCore.SwaggerGen": "6.5.0", "Swashbuckle.AspNetCore.SwaggerUI": "6.5.0"}}, "Swashbuckle.AspNetCore.Swagger/6.5.0": {"dependencies": {"Microsoft.OpenApi": "1.4.3"}, "runtime": {"lib/net7.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.5.0": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.5.0"}, "runtime": {"lib/net7.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.5.0": {"runtime": {"lib/net7.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.CodeDom/4.4.0": {"runtime": {"lib/netstandard2.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}}, "System.Configuration.ConfigurationManager/6.0.1": {"dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.922.41905"}}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Drawing.Common/7.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "7.0.0"}, "runtime": {"lib/net7.0/System.Drawing.Common.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "System.Formats.Asn1/8.0.0": {"runtime": {"lib/net7.0/System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.AccessControl/5.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Memory/4.5.4": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.0"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.221.20802"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Caching/6.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.1"}, "runtime": {"lib/net6.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Cng/5.0.0": {"dependencies": {"System.Formats.Asn1": "8.0.0"}}, "System.Security.Cryptography.Pkcs/8.0.0": {"dependencies": {"System.Formats.Asn1": "8.0.0"}, "runtime": {"lib/net7.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/8.0.0": {"runtime": {"lib/net7.0/System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Text.Encoding.CodePages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Text.Encodings.Web/7.0.0": {}, "System.Text.Json/7.0.0": {"dependencies": {"System.Text.Encodings.Web": "7.0.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "7.0.0"}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}}}, "libraries": {"goodkey_common/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Azure.Core/1.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-hENcx03Jyuqv05F4RBEPbxz29UrM3Nbhnr6Wl6NQpoU9BCIbL3XLentrxDCTrH54NLS11Exxi/o8MYgT/cnKFA==", "path": "azure.core/1.35.0", "hashPath": "azure.core.1.35.0.nupkg.sha512"}, "Azure.Identity/1.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-l1Xm2MWOF2Mzcwuarlw8kWQXLZk3UeB55aQXVyjj23aBfDwOZ3gu5GP2kJ6KlmZeZv2TCzw7x4L3V36iNr3gww==", "path": "azure.identity/1.10.3", "hashPath": "azure.identity.1.10.3.nupkg.sha512"}, "BouncyCastle.Cryptography/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-IaVIiYxZLaBulveGDRUx/pBoW/Rc8QeXGF5u2E8xL8RWhVKCgfmtX9NUyGRbnSqnbFQU2zyP3MkXIdH+jUuQBw==", "path": "bouncycastle.cryptography/2.3.0", "hashPath": "bouncycastle.cryptography.2.3.0.nupkg.sha512"}, "FileSignatures/4.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-vhJMpLzZeGQaxSuv53jo5kDzYBFC6opgP54sBRvf17gh8COpGwzW2pRxd1MrI1RqlClH3VhtlyJs0QyYU9gd/w==", "path": "filesignatures/4.4.1", "hashPath": "filesignatures.4.4.1.nupkg.sha512"}, "Handlebars.Net/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-Od7MWDfGxYKRtxETFMlcvCrY8hAqyuXZDX4EsOfiI/jzh+PVBuVxazHBC1HmVqTKX1JnRtoxIMcH95K9UFlYog==", "path": "handlebars.net/2.1.4", "hashPath": "handlebars.net.2.1.4.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "MailKit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-jVmB3Nr0JpqhyMiXOGWMin+QvRKpucGpSFBCav9dG6jEJPdBV+yp1RHVpKzxZPfT+0adaBuZlMFdbIciZo1EWA==", "path": "mailkit/4.3.0", "hashPath": "mailkit.4.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/7.0.20": {"type": "package", "serviceable": true, "sha512": "sha512-lIjUAxtqNv+bD+Bnlksu96/haMLfuXA7nASXUnvxXfFWA3YEov7pM5AtvlelvQCtwFvu1LR4SFryHvLur491Qg==", "path": "microsoft.aspnetcore.authentication.jwtbearer/7.0.20", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.7.0.20.nupkg.sha512"}, "Microsoft.AspNetCore.OpenApi/7.0.20": {"type": "package", "serviceable": true, "sha512": "sha512-coCi7bh16o4B2qKbD7fng+4m1vu51L9MzTW+9NZp3NvLgAurDMT1kOg2uuycCN4BtEz3EYXlfJMJ2hK6EU7ISQ==", "path": "microsoft.aspnetcore.openapi/7.0.20", "hashPath": "microsoft.aspnetcore.openapi.7.0.20.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/5.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-6kvhQjY5uBCdBccezFD2smfnpQjQ33cZtUZVrNvxlwoBu6uopM5INH6uSgLI7JRLtlQ3bMPwnhMq4kchsXeZ5w==", "path": "microsoft.data.sqlclient/5.1.5", "hashPath": "microsoft.data.sqlclient.5.1.5.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-wNGM5ZTQCa2blc9ikXQouybGiyMd6IHPVJvAlBEPtr6JepZEOYeDxGyprYvFVeOxlCXs7avridZQ0nYkHzQWCQ==", "path": "microsoft.data.sqlclient.sni.runtime/5.1.1", "hashPath": "microsoft.data.sqlclient.sni.runtime.5.1.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/7.0.20": {"type": "package", "serviceable": true, "sha512": "sha512-Wvmktob+dBPvMZdTj2yRrfDi3ERL/RRcE8EWjr3DuIjWn+6giqD81+oNM/09IthwZ3IsSMHgpiVVmTwAW92gEQ==", "path": "microsoft.entityframeworkcore/7.0.20", "hashPath": "microsoft.entityframeworkcore.7.0.20.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.20": {"type": "package", "serviceable": true, "sha512": "sha512-ds6XFVrh34AWYAedlMo8tVkmxsxyXnwP0N6FTjlq4p1oR6TQktIabUDkSMKgStVYt4c4b4Rus3H/Oad9I4uSlQ==", "path": "microsoft.entityframeworkcore.abstractions/7.0.20", "hashPath": "microsoft.entityframeworkcore.abstractions.7.0.20.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.20": {"type": "package", "serviceable": true, "sha512": "sha512-doouHLWFcYcd08QUh9+4aZu3sk4WySp/aSOHeIzNhMJ58ESQXUAGDvB1sjJ4HmE0tmCea51NGzgtHE5IBmFLGg==", "path": "microsoft.entityframeworkcore.analyzers/7.0.20", "hashPath": "microsoft.entityframeworkcore.analyzers.7.0.20.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/7.0.20": {"type": "package", "serviceable": true, "sha512": "sha512-rjxhuC9nUzBdZYxvTpKxuUGzIlGPf5Aj01HzOj7Y4G00PDM4QM5GdRUHGSujNkuwB3oD2j2zHPxwhIqQCznRfg==", "path": "microsoft.entityframeworkcore.design/7.0.20", "hashPath": "microsoft.entityframeworkcore.design.7.0.20.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/7.0.20": {"type": "package", "serviceable": true, "sha512": "sha512-25GWyYLMm4T+iuxJLhXR/GTzeYR7tIhYUOCIaWkSFTI4FB39yQz8wg5EfyhPUL4Jfhjk94X+yFsff/HoNaCVIA==", "path": "microsoft.entityframeworkcore.relational/7.0.20", "hashPath": "microsoft.entityframeworkcore.relational.7.0.20.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/7.0.20": {"type": "package", "serviceable": true, "sha512": "sha512-ukRrvvgDRbs5At/4zQv84gxi+arXvuwRvUHL1cQOdWyOAvSBBieVSmbFhR8ihbbE1Czo3MduxJ+eF2ztHVYC3A==", "path": "microsoft.entityframeworkcore.sqlserver/7.0.20", "hashPath": "microsoft.entityframeworkcore.sqlserver.7.0.20.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/7.0.20": {"type": "package", "serviceable": true, "sha512": "sha512-tu4A/gRuWxAg0c+7i+SzP5VYDwPUTgcND6ymNxwgcLBXr24tG16sNqUn7/fpMhBTeXIpceHu5DgQ2RJTtwoJag==", "path": "microsoft.entityframeworkcore.tools/7.0.20", "hashPath": "microsoft.entityframeworkcore.tools.7.0.20.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IeimUd0TNbhB4ded3AbgBLQv2SnsiVugDyGV1MvspQFVlA07nDC7Zul7kcwH5jWN3JiTcp/ySE83AIJo8yfKjg==", "path": "microsoft.extensions.caching.abstractions/7.0.0", "hashPath": "microsoft.extensions.caching.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xpidBs2KCE2gw1JrD0quHE72kvCaI3xFql5/Peb2GRtUuZX+dYPoK/NTdVMiM67Svym0M0Df9A3xyU0FbMQhHw==", "path": "microsoft.extensions.caching.memory/7.0.0", "hashPath": "microsoft.extensions.caching.memory.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-f34u2eaqIjNO9YLHBz8rozVZ+TcFiFs0F3r7nUJd7FRkVSxk8u4OpoK226mi49MwexHOR2ibP9MFvRUaLilcQQ==", "path": "microsoft.extensions.configuration.abstractions/7.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-elNeOmkeX3eDVG6pYVeV82p29hr+UKDaBhrZyWvWLw/EVZSYEkZlQdkp0V39k/Xehs2Qa0mvoCvkVj3eQxNQ1Q==", "path": "microsoft.extensions.dependencyinjection/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-h3j/QfmFN4S0w4C2A6X7arXij/M/OVw3uQHSOFxnND4DyAzO1F9eMX7Eti7lU/OkSthEE0WzRsfT/Dmx86jzCw==", "path": "microsoft.extensions.dependencyinjection.abstractions/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-oONNYd71J3LzkWc4fUHl3SvMfiQMYUCo/mDHDEu76hYYxdhdrPYv6fvGv9nnKVyhE9P0h20AU8RZB5OOWQcAXg==", "path": "microsoft.extensions.dependencymodel/7.0.0", "hashPath": "microsoft.extensions.dependencymodel.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nw2muoNrOG5U5qa2ZekXwudUn2BJcD41e65zwmDHb1fQegTX66UokLWZkJRpqSSHXDOWZ5V0iqhbxOEky91atA==", "path": "microsoft.extensions.logging/7.0.0", "hashPath": "microsoft.extensions.logging.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kmn78+LPVMOWeITUjIlfxUPDsI0R6G0RkeAMBmQxAJ7vBJn4q2dTva7pWi65ceN5vPGjJ9q/Uae2WKgvfktJAw==", "path": "microsoft.extensions.logging.abstractions/7.0.0", "hashPath": "microsoft.extensions.logging.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lP1yBnTTU42cKpMozuafbvNtQ7QcBjr/CcK3bYOGEMH55Fjt+iecXjT6chR7vbgCMqy3PG3aNQSZgo/EuY/9qQ==", "path": "microsoft.extensions.options/7.0.0", "hashPath": "microsoft.extensions.options.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "path": "microsoft.extensions.primitives/7.0.0", "hashPath": "microsoft.extensions.primitives.7.0.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.56.0": {"type": "package", "serviceable": true, "sha512": "sha512-rr4zbidvHy9r4NvOAs5hdd964Ao2A0pAeFBJKR95u1CJAVzbd1p6tPTXUZ+5ld0cfThiVSGvz6UHwY6JjraTpA==", "path": "microsoft.identity.client/4.56.0", "hashPath": "microsoft.identity.client.4.56.0.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.56.0": {"type": "package", "serviceable": true, "sha512": "sha512-H12YAzEGK55vZ+QpxUzozhW8ZZtgPDuWvgA0JbdIR9UhMUplj29JhIgE2imuH8W2Nw9D8JKygR1uxRFtpSNcrg==", "path": "microsoft.identity.client.extensions.msal/4.56.0", "hashPath": "microsoft.identity.client.extensions.msal.4.56.0.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-xuR8E4Rd96M41CnUSCiOJ2DBh+z+zQSmyrYHdYhD6K4fXBcQGVnRCFQ0efROUYpP+p0zC1BLKr0JRpVuujTZSg==", "path": "microsoft.identitymodel.abstractions/6.35.0", "hashPath": "microsoft.identitymodel.abstractions.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-9wxai3hKgZUb4/NjdRKfQd0QJvtXKDlvmGMYACbEC8DFaicMFCFhQFZq9ZET1kJLwZahf2lfY5Gtcpsx8zYzbg==", "path": "microsoft.identitymodel.jsonwebtokens/6.35.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-jePrSfGAmqT81JDCNSY+fxVWoGuJKt9e6eJ+vT7+quVS55nWl//jGjUQn4eFtVKt4rt5dXaleZdHRB9J9AJZ7Q==", "path": "microsoft.identitymodel.logging/6.35.0", "hashPath": "microsoft.identitymodel.logging.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-BPQhlDzdFvv1PzaUxNSk+VEPwezlDEVADIKmyxubw7IiELK18uJ06RQ9QKKkds30XI+gDu9n8j24XQ8w7fjWcg==", "path": "microsoft.identitymodel.protocols/6.35.0", "hashPath": "microsoft.identitymodel.protocols.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-LMtVqnECCCdSmyFoCOxIE5tXQqkOLrvGrL7OxHg41DIm1bpWtaCdGyVcTAfOQpJXvzND9zUKIN/lhngPkYR8vg==", "path": "microsoft.identitymodel.protocols.openidconnect/6.35.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-RN7lvp7s3Boucg1NaNAbqDbxtlLj5Qeb+4uSS1TeK5FSBVM40P4DKaTKChT43sHyKfh7V0zkrMph6DdHvyA4bg==", "path": "microsoft.identitymodel.tokens/6.35.0", "hashPath": "microsoft.identitymodel.tokens.6.35.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.OpenApi/1.4.3": {"type": "package", "serviceable": true, "sha512": "sha512-rURwggB+QZYcSVbDr7HSdhw/FELvMlriW10OeOzjPT7pstefMo7IThhtNtDudxbXhW+lj0NfX72Ka5EDsG8x6w==", "path": "microsoft.openapi/1.4.3", "hashPath": "microsoft.openapi.1.4.3.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2nXPrhdAyAzir0gLl8Yy8S5Mnm/uBSQQA7jEsILOS1MTyS7DbmV1NgViMtvV1sfCD1ebITpNwb1NIinKeJgUVQ==", "path": "microsoft.win32.systemevents/7.0.0", "hashPath": "microsoft.win32.systemevents.7.0.0.nupkg.sha512"}, "MimeKit/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-pKvznVpvK35cJb21LdjvhB3MW2FrlAqG3PH36uVKHhlgnfMS5tIRsKJEMgo0hiPyJJhN2f1llHptu2w8CvF9Vg==", "path": "mimekit/4.4.0", "hashPath": "mimekit.4.4.0.nupkg.sha512"}, "MimeTypesMap/1.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-iOm6Zar+yVROhlyrGGSJTfThvNoHLUeYeQQND9YD/ot/nA2qsWUp9kP2MHTdF9P7I8afW6eCf8vdELLZjDFdSQ==", "path": "mimetypesmap/1.0.8", "hashPath": "mimetypesmap.1.0.8.nupkg.sha512"}, "Mono.TextTemplating/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-KZYeKBET/2Z0gY1WlTAK7+RHTl7GSbtvTLDXEZZojUdAPqpQNDL6tHv7VUpqfX5VEOh+uRGKaZXkuD253nEOBQ==", "path": "mono.texttemplating/2.2.1", "hashPath": "mono.texttemplating.2.2.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Npgsql/6.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-ZPpjFpW0m71pqAMIpUdVZUY74TCTqCXrytbFsMvm8IuAadpfV9K1lwphuyDBKCsc3w1i7TH+aevHQ6TZ+b0ZMw==", "path": "npgsql/6.0.11", "hashPath": "npgsql.6.0.11.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/6.0.29": {"type": "package", "serviceable": true, "sha512": "sha512-GCUgp/4ZltsysSRJUpvpQoFCRJ6OciL5/xZePOpZ8bekthAbLGW3emYaKf2V+1mNXUm9LhCZI7T0ib3I8yNV3A==", "path": "npgsql.entityframeworkcore.postgresql/6.0.29", "hashPath": "npgsql.entityframeworkcore.postgresql.6.0.29.nupkg.sha512"}, "OpenMcdf/*******": {"type": "package", "serviceable": true, "sha512": "sha512-4UU23MN32j7b5CqbdKmMm9xss8k2MlUxGwm25Z72EHva+2rCKsCf+/MGRmw+2p5XPYutSrtuVHWpXLRBgBzFRg==", "path": "openmcdf/*******", "hashPath": "openmcdf.*******.nupkg.sha512"}, "PdfiumViewer/2.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nw4owBmJzDVXoMRjCnqPbET07CPOawwhcNjt+PyRaSxONN9bsFF+cm9kcMsDLEAEaxsiDJo5jls8bCQjvjidkA==", "path": "pdfiumviewer/2.13.0", "hashPath": "pdfiumviewer.2.13.0.nupkg.sha512"}, "SixLabors.ImageSharp/3.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-lFIdxgGDA5iYkUMRFOze7BGLcdpoLFbR+a20kc1W7NepvzU7ejtxtWOg9RvgG7kb9tBoJ3ONYOK6kLil/dgF1w==", "path": "sixlabors.imagesharp/3.1.4", "hashPath": "sixlabors.imagesharp.3.1.4.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-FK05XokgjgwlCI6wCT+D4/abtQkL1X1/B9Oas6uIwHFmYrIO9WUD5aLC9IzMs9GnHfUXOtXZ2S43gN1mhs5+aA==", "path": "swashbuckle.aspnetcore/6.5.0", "hashPath": "swashbuckle.aspnetcore.6.5.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-XWmCmqyFmoItXKFsQSwQbEAsjDKcxlNf1l+/Ki42hcb6LjKL8m5Db69OTvz5vLonMSRntYO1XLqz0OP+n3vKnA==", "path": "swashbuckle.aspnetcore.swagger/6.5.0", "hashPath": "swashbuckle.aspnetcore.swagger.6.5.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y/qW8Qdg9OEs7V013tt+94OdPxbRdbhcEbw4NiwGvf4YBcfhL/y7qp/Mjv/cENsQ2L3NqJ2AOu94weBy/h4KvA==", "path": "swashbuckle.aspnetcore.swaggergen/6.5.0", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.5.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-OvbvxX+wL8skxTBttcBsVxdh73Fag4xwqEU2edh4JMn7Ws/xJHnY/JB1e9RoCb6XpDxUF3hD9A0Z1lEUx40Pfw==", "path": "swashbuckle.aspnetcore.swaggerui/6.5.0", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.5.0.nupkg.sha512"}, "System.CodeDom/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-2sCCb7doXEwtYAbqzbF/8UAeDRMNmPaQbU2q50Psg1J9KzumyVVCgKQY8s53WIPTufNT0DpSe9QRvVjOzfDWBA==", "path": "system.codedom/4.4.0", "hashPath": "system.codedom.4.4.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-jXw9MlUu/kRfEU0WyTptAVueupqIeE3/rl0EZDMlf8pcvJnitQ8HeVEp69rZdaStXwTV72boi/Bhw8lOeO+U2w==", "path": "system.configuration.configurationmanager/6.0.1", "hashPath": "system.configuration.configurationmanager.6.0.1.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Drawing.Common/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-K<PERSON>+oBU38pxkKPxvLcLfIkOV5Ien8ReN78wro7OF5/erwcmortzeFx+iBswlh2Vz6gVne0khocQudGwaO1Ey6A==", "path": "system.drawing.common/7.0.0", "hashPath": "system.drawing.common.7.0.0.nupkg.sha512"}, "System.Formats.Asn1/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AJukBuLoe3QeAF+mfaRKQb2dgyrvt340iMBHYv+VdBzCUM06IxGlvl0o/uPOS7lHnXPN6u8fFRHSHudx5aTi8w==", "path": "system.formats.asn1/8.0.0", "hashPath": "system.formats.asn1.8.0.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-yxGIQd3BFK7F6S62/7RdZk3C/mfwyVxvh6ngd1VYMBmbJ1YZZA9+Ku6suylVtso0FjI0wbElpJ0d27CdsyLpBQ==", "path": "system.identitymodel.tokens.jwt/6.35.0", "hashPath": "system.identitymodel.tokens.jwt.6.35.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SxHB3nuNrpptVk+vZ/F+7OHEpoHUIKKMl02bUmYHQr1r+glbZQxs7pRtsf4ENO29TVm2TH3AEeep2fJcy92oYw==", "path": "system.io.filesystem.accesscontrol/5.0.0", "hashPath": "system.io.filesystem.accesscontrol.5.0.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Caching/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-E0e03kUp5X2k+UAoVl6efmI7uU7JRBWi5EIdlQ7cr0NpBGjHG4fWII35PgsBY9T4fJQ8E4QPsL0rKksU9gcL5A==", "path": "system.runtime.caching/6.0.0", "hashPath": "system.runtime.caching.6.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "path": "system.security.cryptography.cng/5.0.0", "hashPath": "system.security.cryptography.cng.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ULmp3xoOwNYjOYp4JZ2NK/6NdTgiN1GQXzVVN1njQ7LOZ0d0B9vyMnhyqbIi9Qw4JXj1JgCsitkTShboHRx7Eg==", "path": "system.security.cryptography.pkcs/8.0.0", "hashPath": "system.security.cryptography.pkcs.8.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "path": "system.security.cryptography.protecteddata/6.0.0", "hashPath": "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OZIsVplFGaVY90G2SbpgU7EnCoOO5pw1t4ic21dBF3/1omrJFpAGoNAVpPyMVOC90/hvgkGG3VFqR13YgZMQfg==", "path": "system.text.encoding.codepages/8.0.0", "hashPath": "system.text.encoding.codepages.8.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OP6umVGxc0Z0MvZQBVigj4/U31Pw72ITihDWP9WiWDm+q5aoe0GaJivsfYGq53o6dxH7DcXWiCTl7+0o2CGdmg==", "path": "system.text.encodings.web/7.0.0", "hashPath": "system.text.encodings.web.7.0.0.nupkg.sha512"}, "System.Text.Json/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DaGSsVqKsn/ia6RG8frjwmJonfos0srquhw09TlT8KRw5I43E+4gs+/bZj4K0vShJ5H9imCuXupb4RmS+dBy3w==", "path": "system.text.json/7.0.0", "hashPath": "system.text.json.7.0.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}}}