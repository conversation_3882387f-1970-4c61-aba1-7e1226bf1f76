﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Models
{
    [Table("show_location_halls")]
    public partial class ShowLocationHall
    {
        [Key]
        [Column("hall_id")]
        public int HallId { get; set; }
        [Column("location_id")]
        public int LocationId { get; set; }
        [Required]
        [Column("hall_name")]
        [StringLength(100)]
        public string HallName { get; set; }
        [Column("hall_code")]
        [StringLength(10)]
        public string HallCode { get; set; }
        [Column("hall_style")]
        [StringLength(100)]
        public string HallStyle { get; set; }
        [Column("hall_floor_type")]
        [StringLength(100)]
        public string HallFloorType { get; set; }
        [Column("hall_floor_plan")]
        [StringLength(255)]
        public string HallFloorPlan { get; set; }
        [Column("banquet_capacity")]
        public int? BanquetCapacity { get; set; }
        [Column("hall_width")]
        [Precision(10, 2)]
        public decimal? HallWidth { get; set; }
        [Column("hall_length")]
        [Precision(10, 2)]
        public decimal? HallLength { get; set; }
        [Column("overhead_height")]
        [Precision(10, 2)]
        public decimal? OverheadHeight { get; set; }
        [Column("hall_area")]
        [Precision(10, 2)]
        public decimal? HallArea { get; set; }
        [Column("is_elec_on_floor")]
        public bool? IsElecOnFloor { get; set; }
        [Column("is_elec_on_ceiling")]
        public bool? IsElecOnCeiling { get; set; }
        [Column("hall_surface")]
        [Precision(10, 2)]
        public decimal? HallSurface { get; set; }
        [Column("hall_ceiling_height")]
        [Precision(10, 2)]
        public decimal? HallCeilingHeight { get; set; }
        [Column("access_door")]
        [StringLength(255)]
        public string AccessDoor { get; set; }
        [Column("loading_docks")]
        public int? LoadingDocks { get; set; }
        [Column("hall_booth_count")]
        public int? HallBoothCount { get; set; }

        [ForeignKey("LocationId")]
        [InverseProperty("ShowLocationHalls")]
        public virtual ShowLocation Location { get; set; }
    }
}