﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class ShowLocationHalls
    {
        public int HallId { get; set; }
        public int LocationId { get; set; }
        public string HallName { get; set; }
        public string HallCode { get; set; }
        public string HallStyle { get; set; }
        public string HallFloorType { get; set; }
        public string HallFloorPlan { get; set; }
        public int? BanquetCapacity { get; set; }
        public decimal? HallWidth { get; set; }
        public decimal? HallLength { get; set; }
        public decimal? OverheadHeight { get; set; }
        public decimal? HallArea { get; set; }
        public bool? IsElecOnFloor { get; set; }
        public bool? IsElecOnCeiling { get; set; }
        public decimal? HallSurface { get; set; }
        public decimal? HallCeilingHeight { get; set; }
        public string AccessDoor { get; set; }
        public int? LoadingDocks { get; set; }
        public int? HallBoothCount { get; set; }

        public virtual ShowLocations Location { get; set; }
    }
}