﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace goodkey_common.DTO
{
    public class GenericRespond<T>
    {
        private T? data;
        private int statusCode;
        private string? message;
        private dynamic Errors;
        public T? Data
        {
            get { return data; }
            set
            {
                data = value;
                UpdateStatusCode();
                UpdateMessage();
            }
        }

        public int StatusCode
        {
            get { return statusCode; }
            set
            {
                statusCode = value;
                UpdateMessage();
            }
        }

        public string? Message
        {
            get { return message; }
            set { message = value; }
        }

        private void UpdateStatusCode()
        {
            StatusCode = (Data != null) ? (Data is bool && (bool)(data as object) == false) ? 406 : 200 : 404;
        }

        private void UpdateMessage()
        {
            Message = StatusCode switch
            {
                200 => "OK",
                201 => "Created",
                204 => "No Content",
                400 => "Bad Request",
                401 => "Unauthorized",
                403 => "Forbidden",
                404 => "Not Found",
                500 => "Internal Server Error",
                _ => "Unknown Error",
            };
        }
    }
}
