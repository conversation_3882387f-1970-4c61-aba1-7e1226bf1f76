﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Models
{
    [Table("provinces")]
    public partial class Province
    {
        public Province()
        {
            AuthUsers = new HashSet<AuthUser>();
            Companies = new HashSet<Company>();
            ShowLocationLocationProvinces = new HashSet<ShowLocation>();
            ShowLocationShippingProvinces = new HashSet<ShowLocation>();
        }

        [Key]
        [Column("Province_ID")]
        public int ProvinceId { get; set; }
        [Column("Province_Name")]
        public string ProvinceName { get; set; }
        [Column("Province_Name_Fr")]
        public string ProvinceNameFr { get; set; }
        [Column("Province_Code")]
        public string ProvinceCode { get; set; }
        [Column("Display_Order")]
        public int DisplayOrder { get; set; }
        [Column("Country_ID")]
        public int? CountryId { get; set; }

        [ForeignKey("CountryId")]
        [InverseProperty("Provinces")]
        public virtual Country Country { get; set; }
        [InverseProperty("Province")]
        public virtual ICollection<AuthUser> AuthUsers { get; set; }
        [InverseProperty("Province")]
        public virtual ICollection<Company> Companies { get; set; }
        [InverseProperty("LocationProvince")]
        public virtual ICollection<ShowLocation> ShowLocationLocationProvinces { get; set; }
        [InverseProperty("ShippingProvince")]
        public virtual ICollection<ShowLocation> ShowLocationShippingProvinces { get; set; }
    }
}