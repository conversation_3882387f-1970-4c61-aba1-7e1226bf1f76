﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Models
{
    [Table("company")]
    public partial class Company
    {
        public Company()
        {
            AuthUsers = new HashSet<AuthUser>();
        }

        [Key]
        [Column("company_id")]
        public int CompanyId { get; set; }
        [Required]
        [Column("company_name")]
        [StringLength(255)]
        public string CompanyName { get; set; }
        [Column("industry_id")]
        public int? IndustryId { get; set; }
        [Column("founded_year")]
        public int? FoundedYear { get; set; }
        [Column("phone")]
        [StringLength(20)]
        public string Phone { get; set; }
        [Column("street")]
        [StringLength(255)]
        public string Street { get; set; }
        [Column("city")]
        [StringLength(255)]
        public string City { get; set; }
        [Column("province_id")]
        public int? ProvinceId { get; set; }
        [Column("postal_code")]
        [StringLength(20)]
        public string PostalCode { get; set; }
        [Column("country_id")]
        public int? CountryId { get; set; }
        [Column("website_url")]
        [StringLength(255)]
        public string WebsiteUrl { get; set; }
        [Column("created_at", TypeName = "timestamp without time zone")]
        public DateTime? CreatedAt { get; set; }

        [ForeignKey("CountryId")]
        [InverseProperty("Companies")]
        public virtual Country Country { get; set; }
        [ForeignKey("IndustryId")]
        [InverseProperty("Companies")]
        public virtual Industry Industry { get; set; }
        [ForeignKey("ProvinceId")]
        [InverseProperty("Companies")]
        public virtual Province Province { get; set; }
        [InverseProperty("Company")]
        public virtual ICollection<AuthUser> AuthUsers { get; set; }
    }
}