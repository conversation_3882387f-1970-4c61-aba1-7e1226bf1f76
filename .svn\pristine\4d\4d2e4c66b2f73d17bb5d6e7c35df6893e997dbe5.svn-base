﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Models
{
    [Table("show_location_contacts")]
    public partial class ShowLocationContact
    {
        [Key]
        [Column("contact_id")]
        public int ContactId { get; set; }
        [Column("location_id")]
        public int LocationId { get; set; }
        [Column("contact_title")]
        [StringLength(100)]
        public string ContactTitle { get; set; }
        [Required]
        [Column("contact_first_name")]
        [StringLength(100)]
        public string ContactFirstName { get; set; }
        [Required]
        [Column("contact_last_name")]
        [StringLength(100)]
        public string ContactLastName { get; set; }
        [Column("contact_email")]
        [StringLength(100)]
        public string ContactEmail { get; set; }
        [Column("contact_telephone")]
        [StringLength(20)]
        public string ContactTelephone { get; set; }
        [Column("contact_ext")]
        [StringLength(10)]
        public string ContactExt { get; set; }
        [Column("contact_cell")]
        [StringLength(20)]
        public string ContactCell { get; set; }
        [Column("contact_fax")]
        [StringLength(20)]
        public string ContactFax { get; set; }

        [ForeignKey("LocationId")]
        [InverseProperty("ShowLocationContacts")]
        public virtual ShowLocation Location { get; set; }
    }
}