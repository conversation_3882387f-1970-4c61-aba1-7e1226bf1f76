{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\Goodkey_API_test\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{E2D1A0D2-FCCF-4B32-A143-6079C8D50BCB}|goodkey_common\\goodkey_common.csproj|c:\\users\\<USER>\\source\\repos\\goodkey_api_test\\goodkey_common\\context\\goodkeycontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E2D1A0D2-FCCF-4B32-A143-6079C8D50BCB}|goodkey_common\\goodkey_common.csproj|solutionrelative:goodkey_common\\context\\goodkeycontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E2D1A0D2-FCCF-4B32-A143-6079C8D50BCB}|goodkey_common\\goodkey_common.csproj|c:\\users\\<USER>\\source\\repos\\goodkey_api_test\\goodkey_common\\infrastructure\\extensions\\repositoryregistrationextenstion.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E2D1A0D2-FCCF-4B32-A143-6079C8D50BCB}|goodkey_common\\goodkey_common.csproj|solutionrelative:goodkey_common\\infrastructure\\extensions\\repositoryregistrationextenstion.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E2D1A0D2-FCCF-4B32-A143-6079C8D50BCB}|goodkey_common\\goodkey_common.csproj|c:\\users\\<USER>\\source\\repos\\goodkey_api_test\\goodkey_common\\repositories\\rolegrouprepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E2D1A0D2-FCCF-4B32-A143-6079C8D50BCB}|goodkey_common\\goodkey_common.csproj|solutionrelative:goodkey_common\\repositories\\rolegrouprepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|c:\\users\\<USER>\\source\\repos\\goodkey_api_test\\goodkeyapi\\controllers\\departmentcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|solutionrelative:goodkeyapi\\controllers\\departmentcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|c:\\users\\<USER>\\source\\repos\\goodkey_api_test\\goodkeyapi\\dto\\role\\requestdtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|solutionrelative:goodkeyapi\\dto\\role\\requestdtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|c:\\users\\<USER>\\source\\repos\\goodkey_api_test\\goodkeyapi\\dto\\province\\responddtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|solutionrelative:goodkeyapi\\dto\\province\\responddtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|c:\\users\\<USER>\\source\\repos\\goodkey_api_test\\goodkeyapi\\dto\\permission\\responddtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|solutionrelative:goodkeyapi\\dto\\permission\\responddtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|c:\\users\\<USER>\\source\\repos\\goodkey_api_test\\goodkeyapi\\dto\\menuitem\\responsedtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|solutionrelative:goodkeyapi\\dto\\menuitem\\responsedtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|c:\\users\\<USER>\\source\\repos\\goodkey_api_test\\goodkeyapi\\dto\\menu\\responddtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|solutionrelative:goodkeyapi\\dto\\menu\\responddtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|c:\\users\\<USER>\\source\\repos\\goodkey_api_test\\goodkeyapi\\dto\\menu\\requestdtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|solutionrelative:goodkeyapi\\dto\\menu\\requestdtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|c:\\users\\<USER>\\source\\repos\\goodkey_api_test\\goodkeyapi\\dto\\auth\\responddtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0774AF4-61D6-4E60-931E-13084A5AE794}|GoodkeyAPI\\goodkey_cms.csproj|solutionrelative:goodkeyapi\\dto\\auth\\responddtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 2385, "SelectedChildIndex": 11, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "RoleGroupRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Goodkey_API_test\\goodkey_common\\Repositories\\RoleGroupRepository.cs", "RelativeDocumentMoniker": "goodkey_common\\Repositories\\RoleGroupRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Goodkey_API_test\\goodkey_common\\Repositories\\RoleGroupRepository.cs", "RelativeToolTip": "goodkey_common\\Repositories\\RoleGroupRepository.cs", "ViewState": "AQIAADgAAAAAAAAAAAAkwGwAAAAMAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T19:34:50.5Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "DepartmentController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Goodkey_API_test\\GoodkeyAPI\\Controllers\\DepartmentController.cs", "RelativeDocumentMoniker": "GoodkeyAPI\\Controllers\\DepartmentController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Goodkey_API_test\\GoodkeyAPI\\Controllers\\DepartmentController.cs", "RelativeToolTip": "GoodkeyAPI\\Controllers\\DepartmentController.cs", "ViewState": "AQIAABAAAAAAAAAAAAAqwCQAAAAJAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T15:07:58.941Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "RequestDtos.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Goodkey_API_test\\GoodkeyAPI\\DTO\\Role\\RequestDtos.cs", "RelativeDocumentMoniker": "GoodkeyAPI\\DTO\\Role\\RequestDtos.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Goodkey_API_test\\GoodkeyAPI\\DTO\\Role\\RequestDtos.cs", "RelativeToolTip": "GoodkeyAPI\\DTO\\Role\\RequestDtos.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAZAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-15T17:39:25.915Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "RespondDtos.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Goodkey_API_test\\GoodkeyAPI\\DTO\\Province\\RespondDtos.cs", "RelativeDocumentMoniker": "GoodkeyAPI\\DTO\\Province\\RespondDtos.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Goodkey_API_test\\GoodkeyAPI\\DTO\\Province\\RespondDtos.cs", "RelativeToolTip": "GoodkeyAPI\\DTO\\Province\\RespondDtos.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAQAAAArAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-15T17:39:25.918Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "RespondDtos.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Goodkey_API_test\\GoodkeyAPI\\DTO\\Permission\\RespondDtos.cs", "RelativeDocumentMoniker": "GoodkeyAPI\\DTO\\Permission\\RespondDtos.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Goodkey_API_test\\GoodkeyAPI\\DTO\\Permission\\RespondDtos.cs", "RelativeToolTip": "GoodkeyAPI\\DTO\\Permission\\RespondDtos.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAZAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-15T17:39:25.92Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "ResponseDtos.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Goodkey_API_test\\GoodkeyAPI\\DTO\\MenuItem\\ResponseDtos.cs", "RelativeDocumentMoniker": "GoodkeyAPI\\DTO\\MenuItem\\ResponseDtos.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Goodkey_API_test\\GoodkeyAPI\\DTO\\MenuItem\\ResponseDtos.cs", "RelativeToolTip": "GoodkeyAPI\\DTO\\MenuItem\\ResponseDtos.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAZAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-15T17:39:25.923Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "RespondDtos.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Goodkey_API_test\\GoodkeyAPI\\DTO\\Menu\\RespondDtos.cs", "RelativeDocumentMoniker": "GoodkeyAPI\\DTO\\Menu\\RespondDtos.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Goodkey_API_test\\GoodkeyAPI\\DTO\\Menu\\RespondDtos.cs", "RelativeToolTip": "GoodkeyAPI\\DTO\\Menu\\RespondDtos.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAZAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-15T17:39:25.925Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "RequestDtos.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Goodkey_API_test\\GoodkeyAPI\\DTO\\Menu\\RequestDtos.cs", "RelativeDocumentMoniker": "GoodkeyAPI\\DTO\\Menu\\RequestDtos.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Goodkey_API_test\\GoodkeyAPI\\DTO\\Menu\\RequestDtos.cs", "RelativeToolTip": "GoodkeyAPI\\DTO\\Menu\\RequestDtos.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAcAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-15T17:39:25.927Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "RespondDtos.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Goodkey_API_test\\GoodkeyAPI\\DTO\\Auth\\RespondDtos.cs", "RelativeDocumentMoniker": "GoodkeyAPI\\DTO\\Auth\\RespondDtos.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Goodkey_API_test\\GoodkeyAPI\\DTO\\Auth\\RespondDtos.cs", "RelativeToolTip": "GoodkeyAPI\\DTO\\Auth\\RespondDtos.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAcAAAABAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-15T17:39:25.93Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "GoodkeyContext.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Goodkey_API_test\\goodkey_common\\Context\\GoodkeyContext.cs", "RelativeDocumentMoniker": "goodkey_common\\Context\\GoodkeyContext.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Goodkey_API_test\\goodkey_common\\Context\\GoodkeyContext.cs", "RelativeToolTip": "goodkey_common\\Context\\GoodkeyContext.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAwAAAAfAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-15T17:39:25.992Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "RepositoryRegistrationExtenstion.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Goodkey_API_test\\goodkey_common\\Infrastructure\\Extensions\\RepositoryRegistrationExtenstion.cs", "RelativeDocumentMoniker": "goodkey_common\\Infrastructure\\Extensions\\RepositoryRegistrationExtenstion.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Goodkey_API_test\\goodkey_common\\Infrastructure\\Extensions\\RepositoryRegistrationExtenstion.cs", "RelativeToolTip": "goodkey_common\\Infrastructure\\Extensions\\RepositoryRegistrationExtenstion.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-15T17:39:25.996Z", "EditorCaption": ""}]}]}]}