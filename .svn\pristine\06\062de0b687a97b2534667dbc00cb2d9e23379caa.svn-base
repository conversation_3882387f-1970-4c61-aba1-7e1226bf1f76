﻿using goodkey_common.Context;
using goodkey_common.Models;
using goodkey_common.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;

namespace goodkey_cms.Repositories
{
    public interface IMenuRepository
    {
        IEnumerable<MenuItem> GetAll();
        IEnumerable<MenuSection> GetMenus();
        MenuSection? GetMenu(int menuId);
        MenuItem? Get(int id); bool SwitchVisibility(int id);
        int? Create(int secId, int? parentId, string name, string? description, string? keywords, string? metaDescription, string? url, int displayOrder, string permissionKey, string? icon, string? target, bool? isVisible, bool? isParent, bool? isDashboard, bool? isStatic, string? direction, IFormFile? image, int? roleId, int? level, string? username);
        bool SetSeo(int id, string? keywords, string? description);
        bool Update(int secId, int id, int? parentId, string name, string? description, string? keywords, string? metaDescription, string? url, int displayOrder, string permissionKey, string? icon, string? target, bool? isVisible, bool? isParent, bool? isDashboard, bool? isStatic, string? direction, IFormFile? image, int? roleId, int? level);
        bool UpdatedStatus(int id, string? username);
        bool MoveDown(int id, string committerUsername);
        bool MoveUp(int id, string committerUsername);
    }

    public class MenuRepository : IMenuRepository
    {
        private readonly GoodkeyContext _context;
        private readonly StorageService _storageService;
        public MenuRepository(GoodkeyContext context, StorageService storageService)
        {
            _context = context;
            _storageService = storageService;
        }

        public int? Create(int secId, int? parentId, string name, string? description, string? keywords, string? metaDescription, string? url, int displayOrder, string permissionKey, string? icon, string? target, bool? isVisible, bool? isParent, bool? isDashboard, bool? isStatic, string? direction, IFormFile? image, int? roleId, int? level, string? username)
        {
            var ImagePath = image != null ? _storageService.UploadFile(image, FileType.Image, "menu", Visibility.Public).RelativePath : null;
            var item = new MenuItem
            {
                ParentId = parentId,
                DisplayOrder = displayOrder,
                PermissionKey = permissionKey,
                IsVisible = isVisible,
                IconName = icon,
                Target = target,
                // CreatedBy = _context.AuthUsers.FirstOrDefault(c => c.Username == username),
                ImagePath = ImagePath,
                Level = level,
                IsParent = isParent,
                IsDashboard = isDashboard,
                Direction = direction,
                SectionId = secId,
                IsStatic = isStatic,
                Name = name,
                Description = description,
                MetaKeywords = keywords,
                MetaDescription = metaDescription,
                Url = url,
            };

            _context.MenuItems.Add(item);
            _context.SaveChanges();
            return item.MenuItemId;
        }



        public bool SwitchVisibility(int id)
        {
            var item = _context.MenuItems.FirstOrDefault(c => c.MenuItemId == id);
            if (item == null) return false;
            item.IsVisible = !item.IsVisible;
            _context.SaveChanges();
            return true;
        }



        public bool Update(int secId, int id, int? parentId, string name, string? description, string? keywords, string? metaDescription, string? url, int displayOrder, string permissionKey, string? icon, string? target, bool? isVisible, bool? isParent, bool? isDashboard, bool? isStatic, string? direction, IFormFile? image, int? roleId, int? level)
        {
            var item = _context.MenuItems.FirstOrDefault(c => c.MenuItemId == id);
            if (item == null) return false;
            item.DisplayOrder = displayOrder;
            item.PermissionKey = permissionKey;
            item.IsVisible = isVisible;
            item.IconName = icon;
            item.Target = target;
            item.ParentId = parentId;
            item.ImagePath = image != null ? _storageService.UploadFile(image, FileType.Image, "menu", Visibility.Public).RelativePath : null;
            item.Level = level;
            item.SectionId = secId;
            item.IsParent = isParent;
            item.IsDashboard = isDashboard;
            item.Direction = direction;
            item.IsStatic = isStatic;

            // Set the properties directly
            item.Name = name;
            item.Description = description;
            item.MetaKeywords = keywords;
            item.MetaDescription = metaDescription;
            item.Url = url;

            _context.SaveChanges();
            return true;
        }

        public bool UpdatedStatus(int id, string? username)
        {
            var item = _context.MenuItems.FirstOrDefault(c => c.MenuItemId == id);
            if (item == null) return false;
            // UpdatedAt and UpdatedBy properties are no longer available in MenuItem
            _context.SaveChanges();
            return true;
        }

        public MenuItem? Get(int id)
        {
            return _context.MenuItems
                .Include(c => c.Section)
                .Include(c => c.InverseParent)
                .Include(c => c.Parent)
                .FirstOrDefault(c => c.MenuItemId == id);
        }

        public IEnumerable<MenuItem> GetAll()
        {
            return _context.MenuItems
                .Include(c => c.Section)
                .Include(c => c.InverseParent)
                    .ThenInclude(c => c.Section)
                .Include(c => c.InverseParent)
                    .ThenInclude(ip => ip.InverseParent)
                        .ThenInclude(c => c.Section)
                .Include(c => c.Parent)
                    .ThenInclude(c => c.Section);
        }

        public MenuSection? GetMenu(int menuId)
        {
            return _context.MenuSections.FirstOrDefault(c => c.Id == menuId);
        }
        public IEnumerable<MenuSection> GetMenus()
        {
            return _context.MenuSections;
        }

        public bool SetSeo(int id, string? keywords, string? description)
        {
            var item = _context.MenuItems.FirstOrDefault(c => c.MenuItemId == id);
            if (item == null) return false;

            item.MetaKeywords = keywords;
            item.MetaDescription = description;

            _context.SaveChanges();
            return true;
        }
        public bool MoveDown(int id, string committerUsername)
        {
            // Find the current menu item
            var menuItem = _context.MenuItems.Include(m => m.InverseParent).FirstOrDefault(m => m.MenuItemId == id);
            if (menuItem == null)
            {
                return false;
            }

            var parentId = menuItem.ParentId;

            // Get all menu items
            var allMenuItems = _context.MenuItems.ToList();

            // Determine siblings (same level items)
            List<MenuItem> siblings;
            if (parentId.HasValue)
            {
                var parent = allMenuItems.FirstOrDefault(m => m.MenuItemId == parentId.Value);
                siblings = parent?.InverseParent.ToList() ?? new List<MenuItem>();
            }
            else
            {
                siblings = allMenuItems.Where(m => m.ParentId == null).ToList();
            }

            // Sort siblings by displayOrder
            siblings = siblings.OrderBy(m => m.DisplayOrder).ToList();

            // Find the index of the current menu item
            var currentIndex = siblings.FindIndex(m => m.MenuItemId == id);

            // If already the last item, do nothing
            if (currentIndex < 0 || currentIndex >= siblings.Count - 1)
            {
                return false;
            }

            // Swap with the next sibling
            var nextSibling = siblings[currentIndex + 1];
            int tempOrder = menuItem.DisplayOrder;
            menuItem.DisplayOrder = nextSibling.DisplayOrder;
            nextSibling.DisplayOrder = tempOrder;

            // Update last modified fields
            // menuItem.UpdatedAt = DateTime.Now;
            // menuItem.UpdatedBy = _context.AuthUsers.FirstOrDefault(u => u.Username == committerUsername);
            // nextSibling.UpdatedAt = DateTime.Now;
            // nextSibling.UpdatedBy = _context.AuthUsers.FirstOrDefault(u => u.Username == committerUsername);

            // Save changes
            _context.SaveChanges();
            return true;
        }
        public bool MoveUp(int id, string committerUsername)
        {
            // Find the current menu item
            var menuItem = _context.MenuItems.Include(m => m.InverseParent).FirstOrDefault(m => m.MenuItemId == id);
            if (menuItem == null)
            {
                return false;
            }

            var parentId = menuItem.ParentId;

            // Get all menu items
            var allMenuItems = _context.MenuItems.ToList();

            // Determine siblings (same level items)
            List<MenuItem> siblings;
            if (parentId.HasValue)
            {
                var parent = allMenuItems.FirstOrDefault(m => m.MenuItemId == parentId.Value);
                siblings = parent?.InverseParent.ToList() ?? new List<MenuItem>();
            }
            else
            {
                siblings = allMenuItems.Where(m => m.ParentId == null).ToList();
            }

            // Sort siblings by displayOrder
            siblings = siblings.OrderBy(m => m.DisplayOrder).ToList();

            // Find the index of the current menu item
            var currentIndex = siblings.FindIndex(m => m.MenuItemId == id);

            // If already the first item, do nothing
            if (currentIndex <= 0)
            {
                return false;
            }

            // Swap with the previous sibling
            var previousSibling = siblings[currentIndex - 1];
            int tempOrder = menuItem.DisplayOrder;
            menuItem.DisplayOrder = previousSibling.DisplayOrder;
            previousSibling.DisplayOrder = tempOrder;

            // Update last modified fields
            // menuItem.UpdatedAt = DateTime.Now;
            // menuItem.UpdatedBy = _context.AuthUsers.FirstOrDefault(u => u.Username == committerUsername);
            // previousSibling.UpdatedAt = DateTime.Now;
            // previousSibling.UpdatedBy = _context.AuthUsers.FirstOrDefault(u => u.Username == committerUsername);

            // Save changes
            _context.SaveChanges();
            return true;
        }

    }
}
