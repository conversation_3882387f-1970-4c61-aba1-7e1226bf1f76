// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class AuthUser
    {
        public AuthUser()
        {
            AuthGroupRole = new HashSet<AuthGroupRole>();
            CompanyCreatedBy = new HashSet<Company>();
            CompanyUpdatedby = new HashSet<Company>();
            DepartmentCreatedBy = new HashSet<Department>();
            DepartmentUpdatedBy = new HashSet<Department>();
            InverseArchivedBy = new HashSet<AuthUser>();
            InverseCreatedBy = new HashSet<AuthUser>();
            InverseUpdatedBy = new HashSet<AuthUser>();
            TaxProvinceCreatedBy = new HashSet<TaxProvince>();
            TaxProvinceUpdatedBy = new HashSet<TaxProvince>();
            TaxProvinceUpdatedIsActiveBy = new HashSet<TaxProvince>();
            WarehouseContactPerson = new HashSet<Warehouse>();
            WarehouseCreatedBy = new HashSet<Warehouse>();
            WarehouseLastUpdateActiveBy = new HashSet<Warehouse>();
            WarehouseUpdatedBy = new HashSet<Warehouse>();
        }

        public int UserId { get; set; }
        public int? RoleId { get; set; }
        public string Username { get; set; }
        public string PasswordHash { get; set; }
        public int? StatusId { get; set; }
        public int? SalutationId { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public int? DepartmentId { get; set; }
        public int? CompanyId { get; set; }
        public string WorkEmail { get; set; }
        public string WorkPhoneNumber { get; set; }
        public string MobileNumber { get; set; }
        public bool? IsVerified { get; set; }
        public bool? IsActive { get; set; }
        public bool? IsArchived { get; set; }
        public string VerificationToken { get; set; }
        public DateTime? VerificationSentDate { get; set; }
        public string VerificationEmail { get; set; }
        public int? CreatedById { get; set; }
        public DateTime? CreatedDate { get; set; }
        public int? UpdatedById { get; set; }
        public DateTime? UpdateDate { get; set; }
        public int? ArchivedById { get; set; }
        public DateTime? ArchivedDate { get; set; }
        public int? AuthGroupId { get; set; }

        public virtual AuthUser ArchivedBy { get; set; }
        public virtual AuthGroup AuthGroup { get; set; }
        public virtual Company Company { get; set; }
        public virtual AuthUser CreatedBy { get; set; }
        public virtual Department Department { get; set; }
        public virtual AuthRole Role { get; set; }
        public virtual Salutation Salutation { get; set; }
        public virtual AuthStatus Status { get; set; }
        public virtual AuthUser UpdatedBy { get; set; }
        public virtual ICollection<AuthGroupRole> AuthGroupRole { get; set; }
        public virtual ICollection<Company> CompanyCreatedBy { get; set; }
        public virtual ICollection<Company> CompanyUpdatedby { get; set; }
        public virtual ICollection<Department> DepartmentCreatedBy { get; set; }
        public virtual ICollection<Department> DepartmentUpdatedBy { get; set; }
        public virtual ICollection<AuthUser> InverseArchivedBy { get; set; }
        public virtual ICollection<AuthUser> InverseCreatedBy { get; set; }
        public virtual ICollection<AuthUser> InverseUpdatedBy { get; set; }
        public virtual ICollection<TaxProvince> TaxProvinceCreatedBy { get; set; }
        public virtual ICollection<TaxProvince> TaxProvinceUpdatedBy { get; set; }
        public virtual ICollection<TaxProvince> TaxProvinceUpdatedIsActiveBy { get; set; }
        public virtual ICollection<Warehouse> WarehouseContactPerson { get; set; }
        public virtual ICollection<Warehouse> WarehouseCreatedBy { get; set; }
        public virtual ICollection<Warehouse> WarehouseLastUpdateActiveBy { get; set; }
        public virtual ICollection<Warehouse> WarehouseUpdatedBy { get; set; }
    }
}