using goodkey_cms.DTO.Role;
using goodkey_cms.DTO.RoleGroup;
using goodkey_cms.Infrastructure.Binders;
using goodkey_cms.Repositories;
using goodkey_common.DTO;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class RoleGroupsController : ControllerBase
    {
        private readonly IRoleGroupRepository _repo;
        public RoleGroupsController(IRoleGroupRepository repo)
        {
            _repo = repo;
        }

        [HttpGet]
        public GenericRespond<IEnumerable<BasicRoleGroupDetail>> Get()
        {
            return new()
            {
                Data = _repo.GetAll().Select(item =>
                    new BasicRoleGroupDetail()
                    {
                        Id = item.GroupId,
                        Name = item.GroupName,
                        MinLevel = item.MinLevel ?? 1,
                        MaxLevel = item.MaxLevel ?? item.MinLevel ?? 1,
                        Roles = item.AuthGroupRole?.Select(gr => new BasicRoleDetail
                        {
                            Id = gr.Role.RoleId,
                            Name = gr.Role.Name,
                            Level = gr.Role.Level ?? 1,
                            UserCount = gr.Role.AuthUser?.Count() ?? 0
                        })
                    }
                )
            };
        }

        [HttpGet("{id}")]
        public GenericRespond<RoleGroupDetail?> Get(int id)
        {
            var item = _repo.Get(id);
            if (item == null)
            {
                return new GenericRespond<RoleGroupDetail?>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "Role group not found"
                };
            }

            return new()
            {
                Data = new RoleGroupDetail()
                {
                    Id = item.GroupId,
                    Name = item.GroupName,
                    MinLevel = item.MinLevel ?? 1,
                    MaxLevel = item.MaxLevel ?? item.MinLevel ?? 1,
                    Roles = item.AuthGroupRole?.Select(gr => new BasicRoleDetail
                    {
                        Id = gr.Role.RoleId,
                        Name = gr.Role.Name,
                        Level = gr.Role.Level ?? 1,
                        UserCount = gr.Role.AuthUser?.Count() ?? 0
                    })
                }
            };
        }

        [HttpPost]
        public GenericRespond<int?> Create([FromBody] RoleGroupData data)
        {
            var item = _repo.Create(data.Name, data.MinLevel, data.MaxLevel);

            if (item == null)
            {
                return new GenericRespond<int?>
                {
                    Data = null,
                    StatusCode = 400,
                    Message = "Failed to create role group. A role group with this name may already exist or level ranges may overlap."
                };
            }

            return new GenericRespond<int?>
            {
                Data = item,
                StatusCode = 201,
                Message = "Role group created successfully."
            };
        }

        [HttpPatch("{id}")]
        public GenericRespond<bool> Update(int id, [FromBody] RoleGroupData data)
        {
            var success = _repo.Update(id, data.Name, data.MinLevel, data.MaxLevel);

            return new GenericRespond<bool>
            {
                Data = success,
                StatusCode = success ? 200 : 400,
                Message = success ? "Role group updated successfully." : "Failed to update role group or level ranges may overlap."
            };
        }

        [HttpGet("{id}/roles")]
        public GenericRespond<IEnumerable<BasicRoleDetail>> GetRoles(int id)
        {
            var roles = _repo.GetRoles(id);

            return new GenericRespond<IEnumerable<BasicRoleDetail>>
            {
                Data = roles.Select(r => new BasicRoleDetail
                {
                    Id = r.RoleId,
                    Name = r.Name,
                    Level = r.Level ?? 1,
                    UserCount = r.AuthUser?.Count() ?? 0
                })
            };
        }

        [HttpGet("{id}/roles/conflicts")]
        public GenericRespond<IEnumerable<int>> GetRoleConflicts(int id, [FromQuery] int[] roleIds)
        {
            var conflictingRoles = _repo.GetConflictingRoles(roleIds, id);

            return new GenericRespond<IEnumerable<int>>
            {
                Data = conflictingRoles,
                StatusCode = 200,
                Message = conflictingRoles.Any() ? "Some roles are already assigned to other groups." : "No conflicts found."
            };
        }

        [HttpPatch("{id}/roles")]
        public GenericRespond<bool> SetRoles(int id, [FromBody] RoleGroupRoleData data)
        {
            var username = Request.HttpContext.GetUsername();
            if (string.IsNullOrEmpty(username))
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }

            var success = _repo.SetRoles(id, data.RoleIds, username);

            return new GenericRespond<bool>
            {
                Data = success,
                StatusCode = success ? 200 : 400,
                Message = success ? "Role group roles updated successfully. Any conflicting roles have been moved from other groups." : "Failed to update role group roles."
            };
        }
    }
}
