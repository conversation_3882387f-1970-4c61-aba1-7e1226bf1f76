using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_cms.Repositories
{
    public interface IRoleGroupRepository
    {
        IEnumerable<AuthGroup> GetAll();
        AuthGroup? Get(int id);
        int? Create(string name, int? level);
        bool Update(int id, string name, int? level);
        bool SetRoles(int id, IEnumerable<int> roleIds, string username);
        IEnumerable<AuthRole> GetRoles(int id);
    }

    public class RoleGroupRepository : IRoleGroupRepository
    {
        private readonly GoodkeyContext _context;

        public RoleGroupRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public int? Create(string name, int? level)
        {

            // Check if a group with the same name already exists
            if (_context.AuthGroup.Any(g => g.GroupName == name))
            {
                return null;
            }

            var item = new AuthGroup()
            {
                GroupName = name,
                Level = level ?? 1 // Default level is 1 if not specified
            };

            _context.AuthGroup.Add(item);
            _context.SaveChanges();
            return item.GroupId;

        }

        public AuthGroup? Get(int id)
        {
            return _context.AuthGroup
                // .Include(g => g.AuthGroupRole)
                //     .ThenInclude(gr => gr.Role)
                .FirstOrDefault(g => g.GroupId == id);
        }

        public IEnumerable<AuthGroup> GetAll()
        {
            return _context.AuthGroup
                .OrderBy(g => g.Level)
                .ThenBy(g => g.GroupName);
        }

        public IEnumerable<AuthRole> GetRoles(int id)
        {
            var group = _context.AuthGroup
                .Include(g => g.AuthGroupRole)
                    .ThenInclude(gr => gr.Role)
                .FirstOrDefault(g => g.GroupId == id);

            return group?.AuthGroupRole.Select(gr => gr.Role) ?? new List<AuthRole>();
        }

        public bool SetRoles(int id, IEnumerable<int> roleIds, string username)
        {

            var group = _context.AuthGroup
                .Include(g => g.AuthGroupRole)
                .FirstOrDefault(g => g.GroupId == id);

            if (group == null)
                return false;

            var user = _context.AuthUser.FirstOrDefault(u => u.Username.ToLower() == username.ToLower());
            if (user == null)
                return false;

            // Remove existing role associations
            _context.AuthGroupRole.RemoveRange(group.AuthGroupRole);

            // Add new role associations
            foreach (var roleId in roleIds)
            {
                var role = _context.AuthRole.FirstOrDefault(r => r.RoleId == roleId);
                if (role == null)
                    continue;

                var groupRole = new AuthGroupRole
                {
                    GroupId = group.GroupId,
                    RoleId = role.RoleId,
                    CreatedById = user.UserId,
                    CreatedDate = DateTime.Now
                };

                _context.AuthGroupRole.Add(groupRole);
            }

            _context.SaveChanges();
            return true;

        }

        public bool Update(int id, string name, int? level)
        {

            var group = _context.AuthGroup.FirstOrDefault(g => g.GroupId == id);
            if (group == null)
                return false;

            // Check if another group with the same name already exists
            if (_context.AuthGroup.Any(g => g.GroupName == name && g.GroupId != id))
            {
                return false;
            }

            group.GroupName = name;
            if (level.HasValue)
            {
                group.Level = level.Value;
            }

            _context.SaveChanges();
            return true;

        }
    }
}
