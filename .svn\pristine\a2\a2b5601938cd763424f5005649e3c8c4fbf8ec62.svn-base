﻿using goodkey_common.Context;
using goodkey_common.Models;

namespace goodkey_cms.Repositories
{
    public interface IDepartmentRepository
    {
        IEnumerable<Department> GetAll();
        Department? Get(int id);
        bool Add(string name, string username);
        bool Update(int id, string name, string username);
    }

    public class DepartmentRepository : IDepartmentRepository
    {
        private readonly GoodkeyContext _context;

        public DepartmentRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public bool Add(string name, string username)
        {
            AuthUser? user = _context.AuthUser.FirstOrDefault(x => x.Username == username);

            if (user == null)
            {
                return false;
            }

            var existingDepartment = _context.Department.FirstOrDefault(d => d.Name == name);
            if (existingDepartment != null)
            {
                return false;
            }

            string GenerateRandomCode(int length = 3)
            {
                const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
                var random = new Random();
                return new string(Enumerable.Range(0, length)
                    .Select(_ => chars[random.Next(chars.Length)]).ToArray());
            }

            // Generate unique 3-letter code
            string code;
            do
            {
                code = GenerateRandomCode();
            } while (_context.Department.Any(d => d.Code == code));

            var department = new Department()
            {
                Name = name,
                CreatedById = user.UserId,
                Code = code,
                CreatedAt = DateTime.Now,
            };
            _context.Department.Add(department);
            _context.SaveChanges();
            return true;
        }

        public Department? Get(int id)
        {
            return _context.Department.FirstOrDefault(x => x.DepartmentId == id);
        }

        public IEnumerable<Department> GetAll()
        {
            return _context.Department;
        }

        public bool Update(int id, string name, string username)
        {
            AuthUser? user = _context.AuthUser.FirstOrDefault(x => x.Username == username);
            var department = _context.Department.FirstOrDefault(x => x.DepartmentId == id);

            if (department == null || user == null)
            {
                return false;
            }

            int userId = user.UserId;

            department.Name = name;
            department.UpdatedById = user.UserId;
            department.UpdatedAt = DateTime.Now;

            _context.Department.Update(department);
            _context.SaveChanges();
            return true;
        }
    }
}
