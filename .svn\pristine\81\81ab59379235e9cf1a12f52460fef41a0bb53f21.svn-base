﻿using goodkey_cms.DTO;
using goodkey_cms.Repositories;
using goodkey_common.DTO;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_common.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class SalutationController : Controller
    {
        private readonly ISalutationRepository _repo;

        public SalutationController(ISalutationRepository repo)
        {
            _repo = repo;
        }


        [HttpGet("[action]")]
        public GenericRespond<IEnumerable<BasicDetail>> GetBrief()
        {
            return new()
            {
                Data = _repo.GetAll().Select(item =>

                new BasicDetail()
                {
                    Id = item.SalutationId,
                    Name = item.SalutationName
                }
            )
            };
        }
    }
}



