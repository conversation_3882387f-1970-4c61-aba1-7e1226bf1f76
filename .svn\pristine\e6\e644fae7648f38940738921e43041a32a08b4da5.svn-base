﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Models
{
    [Table("language")]
    public partial class Language
    {
        public Language()
        {
            AuthUsers = new HashSet<AuthUser>();
        }

        [Key]
        [Column("lang_code")]
        [StringLength(3)]
        public string LangCode { get; set; }
        [Required]
        [Column("lang_culture")]
        [StringLength(10)]
        public string LangCulture { get; set; }
        [Required]
        [Column("lang_eng")]
        [StringLength(50)]
        public string LangEng { get; set; }
        [Required]
        [Column("lang_fra")]
        [StringLength(50)]
        public string LangFra { get; set; }
        [Required]
        [Column("lang_native")]
        [StringLength(50)]
        public string LangNative { get; set; }
        [Column("display_order")]
        public int? DisplayOrder { get; set; }

        [InverseProperty("PreferredLang")]
        public virtual ICollection<AuthUser> AuthUsers { get; set; }
    }
}