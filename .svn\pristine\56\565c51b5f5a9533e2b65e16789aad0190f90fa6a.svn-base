﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_cms.Repositories
{
    public interface ISalutationRepository
    {
        IEnumerable<Salutation> GetAll();
    }

    public class SalutationRepository : ISalutationRepository
    {
        private readonly GoodkeyContext _context;
        public SalutationRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public IEnumerable<Salutation> GetAll()
        {
            return _context.Salutation.ToList();
        }
    }
}
