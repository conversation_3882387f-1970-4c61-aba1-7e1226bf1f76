﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class AuthGroupRole
    {
        public int GroupId { get; set; }
        public int RoleId { get; set; }
        public int? CreatedById { get; set; }
        public DateTime? CreatedDate { get; set; }

        public virtual AuthUser CreatedBy { get; set; }
        public virtual AuthGroup Group { get; set; }
        public virtual AuthRole Role { get; set; }
    }
}