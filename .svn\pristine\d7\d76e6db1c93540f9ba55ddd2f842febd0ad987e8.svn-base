﻿namespace goodkey_cms.DTO
{
    public class MenuItemDto
    {
        public int MenuItemId { get; set; }

        public string Title { get; set; } = null!;

        public string Path { get; set; } = null!;

        public string? Link { get; set; }

        public bool? NewTab { get; set; }

        public string? PermissionKey { get; set; }

        public string? Icon { get; set; }

        public int? ParentMenuItemId { get; set; }

        public int? MenuItemLevelId { get; set; }

        public int? DisplayOrder { get; set; }

        public string? Section { get; set; }

        public List<MenuItemDto> Submenu { get; set; } = new List<MenuItemDto>();
    }
}
