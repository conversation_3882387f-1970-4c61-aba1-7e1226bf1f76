﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Models
{
    [Table("menu_section")]
    public partial class MenuSection
    {
        public MenuSection()
        {
            MenuItems = new HashSet<MenuItem>();
        }

        [Key]
        [Column("id")]
        public int Id { get; set; }
        [Required]
        [Column("name")]
        public string Name { get; set; }
        [Column("is_dashboard")]
        public bool IsDashboard { get; set; }

        [InverseProperty("Section")]
        public virtual ICollection<MenuItem> MenuItems { get; set; }
    }
}