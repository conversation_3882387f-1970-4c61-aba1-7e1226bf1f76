﻿{
   "CodeGenerationMode": 2,
   "ContextClassName": "GoodkeyContext",
   "ContextNamespace": null,
   "DefaultDacpacSchema": null,
   "FilterSchemas": false,
   "IncludeConnectionString": false,
   "ModelNamespace": null,
   "OutputContextPath": "Context",
   "OutputPath": "Models",
   "PreserveCasingWithRegex": true,
   "ProjectRootNamespace": "goodkey_common",
   "Schemas": null,
   "SelectedHandlebarsLanguage": 0,
   "SelectedToBeGenerated": 0,
   "Tables": [
      {
         "Name": "public.auth_group",
         "ObjectType": 0
      },
      {
         "Name": "public.auth_permission",
         "ObjectType": 0
      },
      {
         "Name": "public.auth_role",
         "ObjectType": 0
      },
      {
         "Name": "public.auth_role_permission",
         "ObjectType": 0
      },
      {
         "Name": "public.auth_status",
         "ObjectType": 0
      },
      {
         "Name": "public.auth_user",
         "ObjectType": 0
      },
      {
         "Name": "public.company",
         "ObjectType": 0
      },
      {
         "Name": "public.countries",
         "ObjectType": 0
      },
      {
         "Name": "public.department",
         "ObjectType": 0
      },
      {
         "Name": "public.industry",
         "ObjectType": 0
      },
      {
         "Name": "public.language",
         "ObjectType": 0
      },
      {
         "Name": "public.menu",
         "ObjectType": 0
      },
      {
         "Name": "public.menu_item",
         "ObjectType": 0
      },
      {
         "Name": "public.menu_item_role",
         "ObjectType": 0
      },
      {
         "Name": "public.menu_role",
         "ObjectType": 0
      },
      {
         "Name": "public.menu_section",
         "ObjectType": 0
      },
      {
         "Name": "public.provinces",
         "ObjectType": 0
      },
      {
         "Name": "public.salutation",
         "ObjectType": 0
      },
      {
         "Name": "public.show_location_contacts",
         "ObjectType": 0
      },
      {
         "Name": "public.show_location_halls",
         "ObjectType": 0
      },
      {
         "Name": "public.show_locations",
         "ObjectType": 0
      },
      {
         "Name": "public.tax_province",
         "ObjectType": 0
      },
      {
         "Name": "public.tax_type",
         "ObjectType": 0
      }
   ],
   "UiHint": "GoodKey_Dev",
   "UseBoolPropertiesWithoutDefaultSql": false,
   "UseDatabaseNames": false,
   "UseDbContextSplitting": false,
   "UseFluentApiOnly": true,
   "UseHandleBars": false,
   "UseHierarchyId": false,
   "UseInflector": false,
   "UseLegacyPluralizer": false,
   "UseManyToManyEntity": false,
   "UseNoConstructor": false,
   "UseNoDefaultConstructor": false,
   "UseNoNavigations": false,
   "UseNoObjectFilter": false,
   "UseNodaTime": false,
   "UseNullableReferences": false,
   "UseSchemaFolders": false,
   "UseSpatial": false,
   "UseT4": false
}