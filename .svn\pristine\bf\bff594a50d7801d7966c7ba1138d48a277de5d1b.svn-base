﻿
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Globalization;

namespace goodkey_common.Middlewares
{
    public class RespondCleanserMiddleware
    {
        private readonly RequestDelegate _next;

        public RespondCleanserMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task Invoke(HttpContext context)
        {
            Stream originalBody = context.Response.Body;

            try
            {
                using var memStream = new MemoryStream();
                context.Response.Body = memStream;
                await _next(context);

                if (IsJsonResponse(context))
                {
                    try
                    {
                        memStream.Seek(0, SeekOrigin.Begin);
                        string responseBody = await new StreamReader(memStream).ReadToEndAsync();
                        context.Response.Body = originalBody;

                        JObject responseObject = JObject.Parse(responseBody);
                        RemoveNulls(responseObject);
                        FormatDateTimeProperties(responseObject); // New function to format DateTimes
                        string modifiedResponseBody = JsonConvert.SerializeObject(responseObject);

                        context.Response.ContentType = "application/json";
                        if (responseObject["statusCode"] != null && responseObject["statusCode"]?.Type == JTokenType.Integer)
                        {
                            context.Response.StatusCode = (int)responseObject["statusCode"]!;
                        }
                        await context.Response.WriteAsync(modifiedResponseBody);
                    }

                    catch
                    {
                        memStream.Seek(0, SeekOrigin.Begin);
                        await memStream.CopyToAsync(originalBody);
                    }
                }
                else
                {
                    memStream.Seek(0, SeekOrigin.Begin);
                    await memStream.CopyToAsync(originalBody);
                }
            }
            finally
            {
                context.Response.Body = originalBody;
            }
        }
        private bool IsJsonResponse(HttpContext context)
        {
            string contentType = context.Response.ContentType ?? context.Response.Headers["Content-Type"].ToString();
            return contentType != null && contentType.Contains("application/json");
        }
        public static JObject RemoveNulls(JObject obj)
        {
            foreach (var property in obj.Properties().ToList())
            {
                if (property.Value.Type == JTokenType.Null)
                {
                    property.Remove();
                }
                else if (property.Value.Type == JTokenType.Object)
                {
                    RemoveNulls((JObject)property.Value);
                }
                else if (property.Value.Type == JTokenType.Array)
                {
                    foreach (var item in property.Value)
                    {
                        if (item.Type == JTokenType.Object)
                        {
                            RemoveNulls((JObject)item);
                        }
                    }
                }
            }
            return obj;
        }
        public static void FormatDateTimeProperties(JObject obj)
        {
            foreach (var property in obj.Properties().ToList())
            {
                if (property.Value.Type == JTokenType.Date)
                {
                    DateTime utcDateTime = ((DateTime)property.Value);
                    property.Value = utcDateTime.ToString("yyyy-MM-ddTHH:mm:ss.fffZ", CultureInfo.InvariantCulture);
                }
                else if (property.Value.Type == JTokenType.Object)
                {
                    FormatDateTimeProperties((JObject)property.Value);
                }
                else if (property.Value.Type == JTokenType.Array)
                {
                    foreach (var item in property.Value)
                    {
                        if (item.Type == JTokenType.Object)
                        {
                            FormatDateTimeProperties((JObject)item);
                        }
                    }
                }
            }
        }
    }

}