using goodkey_cms.DTO;
using goodkey_cms.Repositories;
using goodkey_common.DTO;
using goodkey_common.Models;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{

    [Route("[controller]")]
    [ApiController]
    public class MenuItemController : Controller
    {
        private readonly IMenuRepository _repo;
        public MenuItemController(IMenuRepository repo)
        {
            _repo = repo;
        }

        [HttpGet]
        public GenericRespond<IEnumerable<MenuItemDto>> GetAll()
        {
            // Step 1: Get all top-level menu items (where ParentId is null)
            var topMenu = _repo.GetAll().Where(x => x.ParentId == null).
            OrderBy(x => x.DisplayOrder).ToList();

            // Step 2: Create a dictionary to store menu items by their ID for easy lookup (for child-parent relationships)
            var allMenuItems = _repo.GetAll().ToList();
            var menuItemDictionary = allMenuItems.ToDictionary(item => item.MenuItemId);

            // Step 3: Build the hierarchical structure
            var topMenuDtos = topMenu.Select(menu => new MenuItemDto
            {
                MenuItemId = menu.MenuItemId,
                Title = menu.Name ?? string.Empty,
                Path = menu.Url ?? string.Empty,
                Link = menu.Url,
                NewTab = menu.Target == "_blank",
                PermissionKey = menu.PermissionKey,
                Icon = menu.IconName,
                ParentMenuItemId = menu.ParentId,
                MenuItemLevelId = menu.Level,
                DisplayOrder = menu.DisplayOrder,
                Section = menu.Section?.Name,
                Submenu = GetSubMenu(menu.MenuItemId, menuItemDictionary)
            }).ToList();

            // Step 4: Return the hierarchical structure
            return new GenericRespond<IEnumerable<MenuItemDto>>()
            {
                Data = topMenuDtos
            };
        }

        // Helper method to recursively get submenus
        private List<MenuItemDto> GetSubMenu(int parentId, Dictionary<int, MenuItem> menuItemDictionary)
        {
            var submenus = menuItemDictionary.Values
                .Where(item => item.ParentId == parentId) // Find children based on ParentId
                .OrderBy(item => item.DisplayOrder)
                .Select(item => new MenuItemDto
                {
                    MenuItemId = item.MenuItemId,
                    Title = item.Name ?? string.Empty,
                    Path = item.Url ?? string.Empty,
                    Link = item.Url,
                    NewTab = item.Target == "_blank",
                    PermissionKey = item.PermissionKey,
                    Icon = item.IconName,
                    ParentMenuItemId = item.ParentId,
                    MenuItemLevelId = item.Level,
                    DisplayOrder = item.DisplayOrder,
                    Section = item.Section?.Name,
                    Submenu = GetSubMenu(item.MenuItemId, menuItemDictionary) // Recursively get children
                })
                .ToList();

            return submenus.Any() ? submenus : new List<MenuItemDto>();
        }


    }
}
