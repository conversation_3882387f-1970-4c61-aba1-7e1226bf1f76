﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;
using Org.BouncyCastle.Math.EC.Rfc7748;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace goodkey_common.Repositories
{
    public interface ITaxRepository
    {
        IEnumerable<TaxType> GetTaxTypes();
        IEnumerable<TaxProvince> GetTaxProvince();
        TaxProvince? GetTaxProvince(int id);
        bool CreateTaxProvince(int taxTypeId, int provinceId, int displayOrder, decimal taxRate);
        bool UpdateTaxProvince(int id, int displayOrder, decimal taxRate);
    }

    public class TaxRepository : ITaxRepository
    {
        private readonly GoodkeyContext _context;

        public TaxRepository (GoodkeyContext context)
        {
            _context = context;
        }

        public bool CreateTaxProvince(int taxTypeId, int provinceId, int displayOrder, decimal taxRate)
        {
            var taxType = _context.TaxType.FirstOrDefault(x => x.Id == taxTypeId);

            var province = _context.Provinces.FirstOrDefault(x => x.ProvinceId == provinceId);

            if (taxType == null || province == null)
            {
                return false;
            }

            _context.Add(new TaxProvince()
            {
                TaxType = taxType,
                Province = province,
                DisplayOrder = displayOrder,
                TaxRate = taxRate
            });

            _context.SaveChanges();
            return true;
        }

        public IEnumerable<TaxProvince> GetTaxProvince()
        {
            return _context.TaxProvince.Include(x => x.Province).Include(x => x.TaxType);
        }

        public TaxProvince? GetTaxProvince(int id)
        {
            return _context.TaxProvince.Include(x => x.Province).Include(x => x.TaxType).FirstOrDefault(x => x.TaxTypeId == id);
        }

        public IEnumerable<TaxType> GetTaxTypes()
        {
            return _context.TaxType;
        }

        public bool UpdateTaxProvince(int id, int displayOrder, decimal taxRate)
        {
            var taxProvince = _context.TaxProvince.FirstOrDefault(x => x.Id == id);
            if (taxProvince == null) return false;

            taxProvince.DisplayOrder = displayOrder;
            taxProvince.TaxRate = taxRate;
            return true;
        }
    }
}
