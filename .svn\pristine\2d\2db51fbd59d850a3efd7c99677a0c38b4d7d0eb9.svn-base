﻿namespace goodkey_cms.DTO.Auth
{
    public class LoginCredintals
    {
        public string Username { get; set; }
        public string Password { get; set; }
    }
    public class ChangePassword : VerificationCredintals
    {
        public string OldPassword { get; set; }
    }

    public class VerificationCredintals
    {
        public string Password { get; set; }
    }
    public class RequestPasswordReset
    {
        public string Email { get; set; }
    }

    public class RegisterData
    {
        public string Email { get; set; }
        public string Password { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string? Username { get; set; }
    }
}
