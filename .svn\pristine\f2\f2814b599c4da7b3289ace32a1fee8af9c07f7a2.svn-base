﻿using goodkey_common.DTO;
using Microsoft.AspNetCore.Http;
using System;
using System.Net;
using System.Text.Json;
using System.Threading.Tasks;


namespace goodkey_common.Middlewares
{
    public class ErrorHandlingMiddleware
    {
        private readonly RequestDelegate _next;

        public ErrorHandlingMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task Invoke(HttpContext context)
        {
            try
            {
                await _next(context);
                if (context.Response.StatusCode != 200)
                {

                    await HandleExceptionAsync(context, context.Response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                await HandleExceptionAsync(context, (int)HttpStatusCode.InternalServerError);
                throw;
            }
        }

        private static Task HandleExceptionAsync(HttpContext context, int statusCode)
        {
            context.Response.ContentType = "application/json";
            context.Response.StatusCode = statusCode;
            context.Response.Body.Seek(0, SeekOrigin.Begin);
            context.Response.Body.SetLength(0);
            GenericRespond<bool?> errorResponse = new()
            {
                StatusCode = context.Response.StatusCode,
            };

            var jsonErrorResponse = JsonSerializer.Serialize(errorResponse, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            return context.Response.WriteAsync(jsonErrorResponse);
        }
    }
}
