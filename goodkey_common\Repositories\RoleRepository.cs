﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_cms.Repositories
{
    public interface IRoleRepository
    {
        IEnumerable<AuthRole> GetAll();
        AuthRole? Get(int id);
        int? Create(string name, string? description, int level, string username);
        bool Update(int id, string name, string? description, int level, string username);
        bool SetPermission(int id, IEnumerable<string> permissions);
        bool SetMenu(int id, IEnumerable<int> ids);
        IEnumerable<int> GetMenu(int id);
    }

    public class RoleRepository : IRoleRepository
    {
        private readonly GoodkeyContext _context;

        public RoleRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public int? Create(string name, string? description, int level, string username)
        {
            // Find role group that contains this level
            var roleGroup = _context.AuthGroup.FirstOrDefault(g => level >= g.MinLevel && level <= g.<PERSON>Level);
            if (roleGroup == null)
                return null;

            // Check if role name already exists
            if (_context.AuthRole.Any(r => r.Name == name))
                return null;

            // Get user by username
            var user = _context.AuthUser.FirstOrDefault(u => u.Username.ToLower() == username.ToLower());
            if (user == null)
                return null;

            var item = new AuthRole()
            {
                Name = name,
                Description = description ?? string.Empty,
                Level = level
            };

            _context.Add(item);
            _context.SaveChanges();

            // Create the role-group relationship
            var groupRole = new AuthGroupRole
            {
                GroupId = roleGroup.GroupId,
                RoleId = item.RoleId,
                CreatedById = user.UserId,
                CreatedDate = DateTime.Now
            };

            _context.AuthGroupRole.Add(groupRole);
            _context.SaveChanges();

            return item.RoleId;
        }

        public AuthRole? Get(int id)
        {
            return _context.AuthRole
                .Include(c => c.Permission)
                .Include(c => c.AuthGroupRole)
                    .ThenInclude(agr => agr.Group)
                .FirstOrDefault(c => c.RoleId == id);
        }

        public IEnumerable<AuthRole> GetAll()
        {
            return _context.AuthRole;
        }

        public bool SetPermission(int id, IEnumerable<string> permissions)
        {
            var item = _context.AuthRole.Include(c => c.Permission).FirstOrDefault(c => c.RoleId == id);
            if (item == null) return false;

            item.Permission.Clear();

            foreach (var key in permissions)
            {
                var perm = _context.AuthPermission.FirstOrDefault(c => c.Code == key);
                if (perm == null) return false;
                item.Permission.Add(perm);
            }

            _context.SaveChanges();

            return true;
        }

        public bool Update(int id, string name, string? description, int level, string username)
        {
            var item = _context.AuthRole.FirstOrDefault(c => c.RoleId == id);
            if (item == null) return false;

            // Find role group that contains this level
            var roleGroup = _context.AuthGroup.FirstOrDefault(g => level >= g.MinLevel && level <= g.MaxLevel);
            if (roleGroup == null)
                return false;

            // Check if role name already exists (excluding current role)
            if (_context.AuthRole.Any(r => r.Name == name && r.RoleId != id))
                return false;

            // Get user by username
            var user = _context.AuthUser.FirstOrDefault(u => u.Username.ToLower() == username.ToLower());
            if (user == null)
                return false;

            item.Name = name;
            item.Description = description ?? string.Empty;
            item.Level = level;

            // Update role-group relationship
            var existingGroupRole = _context.AuthGroupRole.FirstOrDefault(agr => agr.RoleId == id);
            if (existingGroupRole != null)
            {
                // If the group is changing, delete the old relationship and create a new one
                if (existingGroupRole.GroupId != roleGroup.GroupId)
                {
                    _context.AuthGroupRole.Remove(existingGroupRole);

                    var newGroupRole = new AuthGroupRole
                    {
                        GroupId = roleGroup.GroupId,
                        RoleId = id,
                        CreatedById = user.UserId,
                        CreatedDate = DateTime.Now
                    };
                    _context.AuthGroupRole.Add(newGroupRole);
                }
                // If the group is the same, no need to change the relationship
            }
            else
            {
                // Create new relationship if it doesn't exist
                var groupRole = new AuthGroupRole
                {
                    GroupId = roleGroup.GroupId,
                    RoleId = id,
                    CreatedById = user.UserId,
                    CreatedDate = DateTime.Now
                };
                _context.AuthGroupRole.Add(groupRole);
            }

            _context.SaveChanges();
            return true;
        }

        public IEnumerable<int> GetMenu(int id)
        {
            return _context.AuthRole.Include(c => c.MenuItem).FirstOrDefault(c => c.RoleId == id)?
                .MenuItem.Select(c => c.MenuItemId) ?? new List<int>();

        }

        public bool SetMenu(int id, IEnumerable<int> ids)
        {
            var item = _context.AuthRole.Include(c => c.MenuItem).FirstOrDefault(c => c.RoleId == id);
            if (item == null) return false;
            item.MenuItem.Clear();
            foreach (var key in ids)
            {
                var perm = _context.MenuItem.FirstOrDefault(c => c.MenuItemId == key);
                if (perm == null) return false;
                item.MenuItem.Add(perm);
            }
            ;
            _context.SaveChanges();
            return true;
        }
    }
}

