﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class Provinces
    {
        public Provinces()
        {
            Company = new HashSet<Company>();
            ShowLocationsLocationProvince = new HashSet<ShowLocations>();
            ShowLocationsShippingProvince = new HashSet<ShowLocations>();
            TaxProvince = new HashSet<TaxProvince>();
        }

        public int ProvinceId { get; set; }
        public string ProvinceName { get; set; }
        public string ProvinceNameFr { get; set; }
        public string ProvinceCode { get; set; }
        public int DisplayOrder { get; set; }
        public int? CountryId { get; set; }

        public virtual Countries Country { get; set; }
        public virtual ICollection<Company> Company { get; set; }
        public virtual ICollection<ShowLocations> ShowLocationsLocationProvince { get; set; }
        public virtual ICollection<ShowLocations> ShowLocationsShippingProvince { get; set; }
        public virtual ICollection<TaxProvince> TaxProvince { get; set; }
    }
}