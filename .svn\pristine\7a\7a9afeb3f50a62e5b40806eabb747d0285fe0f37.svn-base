﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_cms.Repositories
{
    public interface IRoleRepository
    {
        IEnumerable<AuthRole> GetAll();
        AuthRole? Get(int id);
        int? Create(string name, string? description);
        bool Update(int id, string name, string? description);
        bool SetPermission(int id, IEnumerable<string> permissions);
        bool SetMenu(int id, IEnumerable<int> ids);
        IEnumerable<int> GetMenu(int id);
    }

    public class RoleRepository : IRoleRepository
    {
        private readonly GoodkeyContext _context;

        public RoleRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public int? Create(string name, string? description)
        {
            var item = new AuthRole()
            {
                Name = name,
                Description = description ?? string.Empty
            };

            _context.Add(item);
            _context.SaveChanges();
            return item.RoleId;
        }

        public AuthRole? Get(int id)
        {
            return _context.AuthRole.Include(c => c.Permission).FirstOrDefault(c => c.RoleId == id);
        }

        public IEnumerable<AuthRole> GetAll()
        {
            return _context.AuthRole;
        }

        public bool SetPermission(int id, IEnumerable<string> permissions)
        {
            var item = _context.AuthRole.Include(c => c.Permission).FirstOrDefault(c => c.RoleId == id);
            if (item == null) return false;

            item.Permission.Clear();

            foreach (var key in permissions)
            {
                var perm = _context.AuthPermission.FirstOrDefault(c => c.Code == key);
                if (perm == null) return false;
                item.Permission.Add(perm);
            }

            _context.SaveChanges();

            return true;
        }

        public bool Update(int id, string name, string? description)
        {
            var item = _context.AuthRole.FirstOrDefault(c => c.RoleId == id);
            if (item == null) return false;

            item.Name = name;
            item.Description = description ?? string.Empty;

            _context.SaveChanges();
            return true;
        }

        public IEnumerable<int> GetMenu(int id)
        {
            return _context.AuthRole.Include(c => c.MenuItem).FirstOrDefault(c => c.RoleId == id)?
                .MenuItem.Select(c => c.MenuItemId) ?? new List<int>();

        }

        public bool SetMenu(int id, IEnumerable<int> ids)
        {
            var item = _context.AuthRole.Include(c => c.MenuItem).FirstOrDefault(c => c.RoleId == id);
            if (item == null) return false;
            item.MenuItem.Clear();
            foreach (var key in ids)
            {
                var perm = _context.MenuItem.FirstOrDefault(c => c.MenuItemId == key);
                if (perm == null) return false;
                item.MenuItem.Add(perm);
            }
            ;
            _context.SaveChanges();
            return true;
        }
    }
}

