﻿using goodkey_common.Context;
using goodkey_common.Models;

namespace goodkey_common.Repositories
{

	public interface IProvinceRepository
	{
		public IEnumerable<Provinces> GetAllProvince();
		public Provinces? Get(int id);
	}

	public class ProvinceRepository : IProvinceRepository
	{
		private readonly GoodkeyContext _context;
		public ProvinceRepository(GoodkeyContext context)
		{
			_context = context;			
		}

		public IEnumerable<Provinces> GetAllProvince()
		{
			return _context.Provinces;
		}

		public Provinces? Get(int id)
		{
			return _context.Provinces.FirstOrDefault(i => i.ProvinceId == id);
		}
	}
}
