﻿
using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_cms.Repositories
{
    public interface IUserRepository
    {
        AuthUser? GetUserByUsername(string username);
        IEnumerable<AuthUser> GetAll();
        AuthUser? GetUserByToken(string token);
        AuthUser? GetById(int id);
        bool SetVerificationToken(int id, string token);
        bool SetPassword(int id, string hashedPassword);
        bool SetVerified(int id, string password);
        bool SetRole(int userId, int roleId, string updatedByUsername);
        bool UpdateUser(AuthUser user);
    }
    public class UserRepository : IUserRepository
    {
        private readonly GoodkeyContext _context;

        public UserRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public IEnumerable<AuthUser> GetAll()
        {
            return _context.AuthUser.Include(r => r.Role);
        }

        public AuthUser? GetEmployee(int id)
        {
            return _context.AuthUser
                .Include(x => x.Role)
                .FirstOrDefault(x => x.UserId == id);
        }



        public AuthUser? GetUserByUsername(string username)
        {
            var user = _context.AuthUser
                .Include(c => c.Role)
                    .ThenInclude(c => c.Permission)

                .Include(x => x.Role)

                .Include(x => x.Role)
                    .ThenInclude(x => x.MenuItem)

                .FirstOrDefault(u => u.Username.ToLower().Equals(username.ToLower()));
            return user;
        }

        public AuthUser? GetUserByToken(string token)
        {
            return _context.AuthUser.FirstOrDefault(u => u.VerificationToken == token);
        }

        public AuthUser? GetById(int id)
        {
            return _context.AuthUser
                .Include(r => r.Role)
                .FirstOrDefault(u => u.UserId == id);
        }


        public bool SetVerificationToken(int id, string token)
        {
            var user = _context.AuthUser.FirstOrDefault(c => c.UserId == id);
            if (user == null) return false;
            user.VerificationToken = token;
            _context.AuthUser.Update(user);
            _context.SaveChanges();
            return true;
        }

        public bool SetVerified(int id, string password)
        {
            var user = _context.AuthUser.FirstOrDefault(c => c.UserId == id);
            if (user == null) return false;
            user.IsVerified = true;
            user.VerificationToken = null;
            user.VerificationSentDate = null;
            user.IsActive = true;
            user.PasswordHash = password;
            _context.AuthUser.Update(user);
            _context.SaveChanges();
            return true;
        }

        public bool SetPassword(int id, string hashedPassword)
        {
            var user = _context.AuthUser.FirstOrDefault(c => c.UserId == id);
            if (user == null) return false;
            user.PasswordHash = hashedPassword;
            _context.AuthUser.Update(user);
            _context.SaveChanges();
            return true;
        }

        public bool SetRole(int userId, int roleId, string updatedByUsername)
        {
            // Load user with Profile and Employee to check user type
            var user = _context.AuthUser
                .FirstOrDefault(u => u.UserId == userId);
            var role = _context.AuthRole.FirstOrDefault(r => r.RoleId == roleId);
            var updatedBy = _context.AuthUser.FirstOrDefault(u => u.Username.ToLower() == updatedByUsername.ToLower());

            if (user == null || role == null || updatedBy == null)
                return false;

            // // Check if user is a client (has Profile and roleId == 7)
            // if (user.Profile != null && user.RoleId == 7)
            // {
            //     // Don't allow changing role for client users
            //     return false;
            // }

            // // Check if user is an employee and trying to change to client role
            // if (user.Employee != null && roleId == 7)
            // {
            //     // Don't allow changing employee to client role
            //     return false;
            // }

            // If all validations pass, update the role
            user.RoleId = roleId;
            user.UpdatedById = updatedBy.UserId;
            // user.UpdatedAt = DateTime.UtcNow;

            _context.SaveChanges();
            return true;
        }

        public bool UpdateUser(AuthUser user)
        {
            try
            {
                user.UpdateDate = DateTime.UtcNow;
                _context.AuthUser.Update(user);
                _context.SaveChanges();
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
