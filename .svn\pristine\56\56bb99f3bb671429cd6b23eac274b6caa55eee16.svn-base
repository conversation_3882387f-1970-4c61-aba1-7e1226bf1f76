﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Models
{
    [Table("industry")]
    [Index("IndustryName", Name = "industry_industry_name_key", IsUnique = true)]
    public partial class Industry
    {
        public Industry()
        {
            Companies = new HashSet<Company>();
        }

        [Key]
        [Column("industry_id")]
        public int IndustryId { get; set; }
        [Required]
        [Column("industry_name")]
        [StringLength(255)]
        public string IndustryName { get; set; }

        [InverseProperty("Industry")]
        public virtual ICollection<Company> Companies { get; set; }
    }
}