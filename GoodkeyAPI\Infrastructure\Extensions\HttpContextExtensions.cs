﻿using goodkey_common.Models;
using System.Security.Claims;

namespace goodkey_cms.Infrastructure.Binders
{
    public static class HttpContextExtensions
    {
        private const string CurrentCultureKey = "Culture";
        private const string CurrentUsernameKey = "Username";
        private const string CurrentUserKey = "User";

        public static void SetCulture(this HttpContext context, string culture)
        {
            context.Items[CurrentCultureKey] = culture;
        }

        public static string GetCulture(this HttpContext context)
        {
            if (context.Items.TryGetValue(CurrentCultureKey, out var culture) && culture is string userObject)
            {
                return userObject;
            }

            return "fr";
        }
        public static void SetUsername(this HttpContext context, string user)
        {
            context.Items[CurrentUsernameKey] = user;
        }

        public static string? GetUsername(this HttpContext context)
        {
            if (context.Items.TryGetValue(CurrentUsernameKey, out var culture) && culture is string userObject)
            {
                return userObject;
            }

            return context.User.Claims?.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
        }
        public static AuthUser SetUser(this HttpContext context, AuthUser user)
        {
            context.Items[CurrentUserKey] = user;
            return user;
        }

        public static AuthUser? GetUser(this HttpContext context)
        {
            if (context.Items.TryGetValue(CurrentUserKey, out var culture) && culture is AuthUser userObject)
            {
                return userObject;
            }

            return null;
        }
    }

}
