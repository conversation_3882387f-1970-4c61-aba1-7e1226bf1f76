﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using goodkey_common.Models;

namespace goodkey_common.Context
{
    public partial class GoodkeyContext : DbContext
    {
        public GoodkeyContext()
        {
        }

        public GoodkeyContext(DbContextOptions<GoodkeyContext> options)
            : base(options)
        {
        }

        public virtual DbSet<AuthGroup> AuthGroup { get; set; }
        public virtual DbSet<AuthPermission> AuthPermission { get; set; }
        public virtual DbSet<AuthRole> AuthRole { get; set; }
        public virtual DbSet<AuthStatus> AuthStatus { get; set; }
        public virtual DbSet<AuthUser> AuthUser { get; set; }
        public virtual DbSet<Company> Company { get; set; }
        public virtual DbSet<Countries> Countries { get; set; }
        public virtual DbSet<Department> Department { get; set; }
        public virtual DbSet<Language> Language { get; set; }
        public virtual DbSet<Menu> Menu { get; set; }
        public virtual DbSet<MenuItem> MenuItem { get; set; }
        public virtual DbSet<MenuSection> MenuSection { get; set; }
        public virtual DbSet<Provinces> Provinces { get; set; }
        public virtual DbSet<Salutation> Salutation { get; set; }
        public virtual DbSet<ShowLocationContacts> ShowLocationContacts { get; set; }
        public virtual DbSet<ShowLocationHalls> ShowLocationHalls { get; set; }
        public virtual DbSet<ShowLocations> ShowLocations { get; set; }
        public virtual DbSet<TaxProvince> TaxProvince { get; set; }
        public virtual DbSet<TaxType> TaxType { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.HasPostgresExtension("dblink");

            modelBuilder.Entity<AuthGroup>(entity =>
            {
                entity.HasKey(e => e.GroupId)
                    .HasName("client_group_pkey");

                entity.ToTable("auth_group");

                entity.Property(e => e.GroupId).HasColumnName("group_id");

                entity.Property(e => e.GroupName)
                    .HasMaxLength(100)
                    .HasColumnName("group_name");
            });

            modelBuilder.Entity<AuthPermission>(entity =>
            {
                entity.HasKey(e => e.PermissionId)
                    .HasName("auth_permission_pkey");

                entity.ToTable("auth_permission");

                entity.Property(e => e.PermissionId).HasColumnName("permission_id");

                entity.Property(e => e.Code)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasColumnName("code");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasColumnName("name");
            });

            modelBuilder.Entity<AuthRole>(entity =>
            {
                entity.HasKey(e => e.RoleId)
                    .HasName("auth_role_pkey");

                entity.ToTable("auth_role");

                entity.Property(e => e.RoleId).HasColumnName("role_id");

                entity.Property(e => e.Description)
                    .HasMaxLength(300)
                    .HasColumnName("description");

                entity.Property(e => e.Name)
                    .HasMaxLength(150)
                    .HasColumnName("name");

                entity.HasMany(d => d.Permission)
                    .WithMany(p => p.Role)
                    .UsingEntity<Dictionary<string, object>>(
                        "AuthRolePermission",
                        l => l.HasOne<AuthPermission>().WithMany().HasForeignKey("PermissionId").HasConstraintName("fk_role_permission_permission"),
                        r => r.HasOne<AuthRole>().WithMany().HasForeignKey("RoleId").HasConstraintName("fk_role_permission_role"),
                        j =>
                        {
                            j.HasKey("RoleId", "PermissionId").HasName("auth_role_permission_pkey");

                            j.ToTable("auth_role_permission");

                            j.IndexerProperty<int>("RoleId").HasColumnName("role_id");

                            j.IndexerProperty<int>("PermissionId").HasColumnName("permission_id");
                        });
            });

            modelBuilder.Entity<AuthStatus>(entity =>
            {
                entity.HasKey(e => e.StatusId)
                    .HasName("employee_status_pkey");

                entity.ToTable("auth_status");

                entity.Property(e => e.StatusId).HasColumnName("status_id");

                entity.Property(e => e.StatusName)
                    .HasMaxLength(100)
                    .HasColumnName("status_name");
            });

            modelBuilder.Entity<AuthUser>(entity =>
            {
                entity.HasKey(e => e.UserId)
                    .HasName("auth_user_pkey");

                entity.ToTable("auth_user");

                entity.Property(e => e.UserId).HasColumnName("user_id");

                entity.Property(e => e.ArchivedById).HasColumnName("archived_by_id");

                entity.Property(e => e.ArchivedDate)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("archived_date");

                entity.Property(e => e.AuthGroupId).HasColumnName("auth_group_id");

                entity.Property(e => e.CompanyId).HasColumnName("company_id");

                entity.Property(e => e.CreatedById).HasColumnName("created_by_id");

                entity.Property(e => e.CreatedDate)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("created_date");

                entity.Property(e => e.DepartmentId).HasColumnName("department_id");

                entity.Property(e => e.FirstName)
                    .HasMaxLength(255)
                    .HasColumnName("first_name");

                entity.Property(e => e.IsActive)
                    .HasColumnName("is_active")
                    .HasDefaultValueSql("true");

                entity.Property(e => e.IsArchived)
                    .HasColumnName("is_archived")
                    .HasDefaultValueSql("false");

                entity.Property(e => e.IsVerified)
                    .HasColumnName("is_verified")
                    .HasDefaultValueSql("false");

                entity.Property(e => e.LastName)
                    .HasMaxLength(255)
                    .HasColumnName("last_name");

                entity.Property(e => e.MobileNumber)
                    .HasMaxLength(15)
                    .HasColumnName("mobile_number");

                entity.Property(e => e.PasswordHash)
                    .HasMaxLength(255)
                    .HasColumnName("password_hash");

                entity.Property(e => e.RoleId).HasColumnName("role_id");

                entity.Property(e => e.SalutationId).HasColumnName("salutation_id");

                entity.Property(e => e.StatusId).HasColumnName("status_id");

                entity.Property(e => e.UpdateDate)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("update_date");

                entity.Property(e => e.UpdatedById).HasColumnName("updated_by_id");

                entity.Property(e => e.Username)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasColumnName("username");

                entity.Property(e => e.VerificationEmail)
                    .HasMaxLength(150)
                    .HasColumnName("verification_email");

                entity.Property(e => e.VerificationSentDate)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("verification_sent_date");

                entity.Property(e => e.VerificationToken)
                    .HasMaxLength(255)
                    .HasColumnName("verification_token");

                entity.Property(e => e.WorkEmail)
                    .HasMaxLength(255)
                    .HasColumnName("work_email");

                entity.Property(e => e.WorkPhoneNumber)
                    .HasMaxLength(15)
                    .HasColumnName("work_phone_number");

                entity.HasOne(d => d.ArchivedBy)
                    .WithMany(p => p.InverseArchivedBy)
                    .HasForeignKey(d => d.ArchivedById)
                    .HasConstraintName("fk_auth_user_archived_by");

                entity.HasOne(d => d.AuthGroup)
                    .WithMany(p => p.AuthUser)
                    .HasForeignKey(d => d.AuthGroupId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_auth_group");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.AuthUser)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_auth_user_company");

                entity.HasOne(d => d.CreatedBy)
                    .WithMany(p => p.InverseCreatedBy)
                    .HasForeignKey(d => d.CreatedById)
                    .HasConstraintName("fk_auth_user_created_by");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.AuthUser)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("fk_auth_user_department");

                entity.HasOne(d => d.Role)
                    .WithMany(p => p.AuthUser)
                    .HasForeignKey(d => d.RoleId)
                    .HasConstraintName("auth_user_role_id_fkey");

                entity.HasOne(d => d.Salutation)
                    .WithMany(p => p.AuthUser)
                    .HasForeignKey(d => d.SalutationId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_auth_user_salutation");

                entity.HasOne(d => d.Status)
                    .WithMany(p => p.AuthUser)
                    .HasForeignKey(d => d.StatusId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_auth_user_status");

                entity.HasOne(d => d.UpdatedBy)
                    .WithMany(p => p.InverseUpdatedBy)
                    .HasForeignKey(d => d.UpdatedById)
                    .HasConstraintName("fk_auth_user_updated_by");
            });

            modelBuilder.Entity<Company>(entity =>
            {
                entity.ToTable("company");

                entity.Property(e => e.CompanyId).HasColumnName("company_id");

                entity.Property(e => e.City)
                    .HasMaxLength(255)
                    .HasColumnName("city");

                entity.Property(e => e.CompanyName)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasColumnName("company_name");

                entity.Property(e => e.CountryId).HasColumnName("country_id");

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("created_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.Property(e => e.CreatedById).HasColumnName("created_by_id");

                entity.Property(e => e.Phone)
                    .HasMaxLength(20)
                    .HasColumnName("phone");

                entity.Property(e => e.PostalCode)
                    .HasMaxLength(20)
                    .HasColumnName("postal_code");

                entity.Property(e => e.ProvinceId).HasColumnName("province_id");

                entity.Property(e => e.Street)
                    .HasMaxLength(255)
                    .HasColumnName("street");

                entity.Property(e => e.WebsiteUrl)
                    .HasMaxLength(255)
                    .HasColumnName("website_url");

                entity.HasOne(d => d.Country)
                    .WithMany(p => p.Company)
                    .HasForeignKey(d => d.CountryId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_country");

                entity.HasOne(d => d.CreatedBy)
                    .WithMany(p => p.CompanyNavigation)
                    .HasForeignKey(d => d.CreatedById)
                    .HasConstraintName("fk_company_created_by");

                entity.HasOne(d => d.Province)
                    .WithMany(p => p.Company)
                    .HasForeignKey(d => d.ProvinceId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_province");
            });

            modelBuilder.Entity<Countries>(entity =>
            {
                entity.HasKey(e => e.CountryId)
                    .HasName("Countries_pkey");

                entity.ToTable("countries");

                entity.Property(e => e.CountryId)
                    .HasColumnName("Country_ID")
                    .UseIdentityAlwaysColumn();

                entity.Property(e => e.CountryCode).HasColumnName("Country_Code");

                entity.Property(e => e.CountryName).HasColumnName("Country_Name");

                entity.Property(e => e.CountryNameFr).HasColumnName("Country_Name_Fr");
            });

            modelBuilder.Entity<Department>(entity =>
            {
                entity.ToTable("department");

                entity.Property(e => e.DepartmentId)
                    .HasColumnName("department_id")
                    .HasDefaultValueSql("nextval('employee_department_department_id_seq'::regclass)");

                entity.Property(e => e.Code)
                    .HasMaxLength(3)
                    .HasColumnName("code");

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("created_at");

                entity.Property(e => e.CreatedById).HasColumnName("created_by_id");

                entity.Property(e => e.Name)
                    .HasMaxLength(150)
                    .HasColumnName("name");

                entity.Property(e => e.UpdatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("updated_at");

                entity.Property(e => e.UpdatedById).HasColumnName("updated_by_id");

                entity.HasOne(d => d.CreatedBy)
                    .WithMany(p => p.DepartmentCreatedBy)
                    .HasForeignKey(d => d.CreatedById)
                    .HasConstraintName("fk_department_created_by");

                entity.HasOne(d => d.UpdatedBy)
                    .WithMany(p => p.DepartmentUpdatedBy)
                    .HasForeignKey(d => d.UpdatedById)
                    .HasConstraintName("fk_department_updated_by");
            });

            modelBuilder.Entity<Language>(entity =>
            {
                entity.HasKey(e => e.LangCode)
                    .HasName("language_pkey");

                entity.ToTable("language");

                entity.Property(e => e.LangCode)
                    .HasMaxLength(3)
                    .HasColumnName("lang_code");

                entity.Property(e => e.DisplayOrder).HasColumnName("display_order");

                entity.Property(e => e.LangCulture)
                    .IsRequired()
                    .HasMaxLength(10)
                    .HasColumnName("lang_culture");

                entity.Property(e => e.LangEng)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("lang_eng");

                entity.Property(e => e.LangFra)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("lang_fra");

                entity.Property(e => e.LangNative)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("lang_native");
            });

            modelBuilder.Entity<Menu>(entity =>
            {
                entity.ToTable("menu");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.HasMany(d => d.Role)
                    .WithMany(p => p.Menu)
                    .UsingEntity<Dictionary<string, object>>(
                        "MenuRole",
                        l => l.HasOne<AuthRole>().WithMany().HasForeignKey("RoleId").HasConstraintName("fk_menu_role_role"),
                        r => r.HasOne<Menu>().WithMany().HasForeignKey("MenuId").HasConstraintName("fk_menu_role_menu"),
                        j =>
                        {
                            j.HasKey("MenuId", "RoleId").HasName("pk_menu_role");

                            j.ToTable("menu_role");

                            j.IndexerProperty<int>("MenuId").HasColumnName("menu_id");

                            j.IndexerProperty<int>("RoleId").HasColumnName("role_id");
                        });
            });

            modelBuilder.Entity<MenuItem>(entity =>
            {
                entity.ToTable("menu_item");

                entity.Property(e => e.MenuItemId).HasColumnName("menu_item_id");

                entity.Property(e => e.Description).HasColumnName("description");

                entity.Property(e => e.Direction)
                    .HasMaxLength(3)
                    .HasColumnName("direction");

                entity.Property(e => e.DisplayOrder).HasColumnName("display_order");

                entity.Property(e => e.IconName).HasColumnName("icon_name");

                entity.Property(e => e.ImagePath).HasColumnName("image_path");

                entity.Property(e => e.IsDashboard).HasColumnName("is_dashboard");

                entity.Property(e => e.IsParent).HasColumnName("is_parent");

                entity.Property(e => e.IsStatic).HasColumnName("is_static");

                entity.Property(e => e.IsVisible).HasColumnName("is_visible");

                entity.Property(e => e.Level).HasColumnName("level");

                entity.Property(e => e.MenuId).HasColumnName("menu_id");

                entity.Property(e => e.MetaDescription).HasColumnName("meta_description");

                entity.Property(e => e.MetaKeywords).HasColumnName("meta_keywords");

                entity.Property(e => e.Name).HasColumnName("name");

                entity.Property(e => e.ParentId).HasColumnName("parent_id");

                entity.Property(e => e.PermissionKey)
                    .HasMaxLength(255)
                    .HasColumnName("permission_key");

                entity.Property(e => e.SectionId).HasColumnName("section_id");

                entity.Property(e => e.Target).HasColumnName("target");

                entity.Property(e => e.Url).HasColumnName("url");

                entity.HasOne(d => d.Menu)
                    .WithMany(p => p.MenuItem)
                    .HasForeignKey(d => d.MenuId)
                    .HasConstraintName("fk_menu_item_menu");

                entity.HasOne(d => d.Parent)
                    .WithMany(p => p.InverseParent)
                    .HasForeignKey(d => d.ParentId)
                    .HasConstraintName("fk_menu_item_parent");

                entity.HasOne(d => d.Section)
                    .WithMany(p => p.MenuItem)
                    .HasForeignKey(d => d.SectionId)
                    .HasConstraintName("fk_menu_item_menu_section");

                entity.HasMany(d => d.Role)
                    .WithMany(p => p.MenuItem)
                    .UsingEntity<Dictionary<string, object>>(
                        "MenuItemRole",
                        l => l.HasOne<AuthRole>().WithMany().HasForeignKey("RoleId").HasConstraintName("fk_menu_item_role_role"),
                        r => r.HasOne<MenuItem>().WithMany().HasForeignKey("MenuItemId").HasConstraintName("fk_menu_item_role_menu_item"),
                        j =>
                        {
                            j.HasKey("MenuItemId", "RoleId").HasName("menu_item_role_pkey");

                            j.ToTable("menu_item_role");

                            j.IndexerProperty<int>("MenuItemId").HasColumnName("menu_item_id");

                            j.IndexerProperty<int>("RoleId").HasColumnName("role_id");
                        });
            });

            modelBuilder.Entity<MenuSection>(entity =>
            {
                entity.ToTable("menu_section");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.IsDashboard).HasColumnName("is_dashboard");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasColumnName("name");
            });

            modelBuilder.Entity<Provinces>(entity =>
            {
                entity.HasKey(e => e.ProvinceId)
                    .HasName("provinces_pkey");

                entity.ToTable("provinces");

                entity.Property(e => e.ProvinceId)
                    .HasColumnName("Province_ID")
                    .UseIdentityAlwaysColumn();

                entity.Property(e => e.CountryId).HasColumnName("Country_ID");

                entity.Property(e => e.DisplayOrder)
                    .HasColumnName("Display_Order")
                    .HasDefaultValueSql("1");

                entity.Property(e => e.ProvinceCode).HasColumnName("Province_Code");

                entity.Property(e => e.ProvinceName).HasColumnName("Province_Name");

                entity.Property(e => e.ProvinceNameFr).HasColumnName("Province_Name_Fr");

                entity.HasOne(d => d.Country)
                    .WithMany(p => p.Provinces)
                    .HasForeignKey(d => d.CountryId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("Provinces_Country_ID_fkey");
            });

            modelBuilder.Entity<Salutation>(entity =>
            {
                entity.ToTable("salutation");

                entity.Property(e => e.SalutationId).HasColumnName("salutation_id");

                entity.Property(e => e.SalutationName)
                    .HasMaxLength(255)
                    .HasColumnName("salutation_name");
            });

            modelBuilder.Entity<ShowLocationContacts>(entity =>
            {
                entity.HasKey(e => e.ContactId)
                    .HasName("show_location_contacts_pkey");

                entity.ToTable("show_location_contacts");

                entity.Property(e => e.ContactId).HasColumnName("contact_id");

                entity.Property(e => e.ContactCell)
                    .HasMaxLength(20)
                    .HasColumnName("contact_cell");

                entity.Property(e => e.ContactEmail)
                    .HasMaxLength(100)
                    .HasColumnName("contact_email");

                entity.Property(e => e.ContactExt)
                    .HasMaxLength(10)
                    .HasColumnName("contact_ext");

                entity.Property(e => e.ContactFax)
                    .HasMaxLength(20)
                    .HasColumnName("contact_fax");

                entity.Property(e => e.ContactFirstName)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("contact_first_name");

                entity.Property(e => e.ContactLastName)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("contact_last_name");

                entity.Property(e => e.ContactTelephone)
                    .HasMaxLength(20)
                    .HasColumnName("contact_telephone");

                entity.Property(e => e.ContactTitle)
                    .HasMaxLength(100)
                    .HasColumnName("contact_title");

                entity.Property(e => e.LocationId).HasColumnName("location_id");

                entity.HasOne(d => d.Location)
                    .WithMany(p => p.ShowLocationContacts)
                    .HasForeignKey(d => d.LocationId)
                    .HasConstraintName("fk_location_contact");
            });

            modelBuilder.Entity<ShowLocationHalls>(entity =>
            {
                entity.HasKey(e => e.HallId)
                    .HasName("show_location_halls_pkey");

                entity.ToTable("show_location_halls");

                entity.Property(e => e.HallId).HasColumnName("hall_id");

                entity.Property(e => e.AccessDoor)
                    .HasMaxLength(255)
                    .HasColumnName("access_door");

                entity.Property(e => e.BanquetCapacity).HasColumnName("banquet_capacity");

                entity.Property(e => e.HallArea)
                    .HasPrecision(10, 2)
                    .HasColumnName("hall_area");

                entity.Property(e => e.HallBoothCount).HasColumnName("hall_booth_count");

                entity.Property(e => e.HallCeilingHeight)
                    .HasPrecision(10, 2)
                    .HasColumnName("hall_ceiling_height");

                entity.Property(e => e.HallCode)
                    .HasMaxLength(10)
                    .HasColumnName("hall_code");

                entity.Property(e => e.HallFloorPlan)
                    .HasMaxLength(255)
                    .HasColumnName("hall_floor_plan");

                entity.Property(e => e.HallFloorType)
                    .HasMaxLength(100)
                    .HasColumnName("hall_floor_type");

                entity.Property(e => e.HallLength)
                    .HasPrecision(10, 2)
                    .HasColumnName("hall_length");

                entity.Property(e => e.HallName)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("hall_name");

                entity.Property(e => e.HallStyle)
                    .HasMaxLength(100)
                    .HasColumnName("hall_style");

                entity.Property(e => e.HallSurface)
                    .HasPrecision(10, 2)
                    .HasColumnName("hall_surface");

                entity.Property(e => e.HallWidth)
                    .HasPrecision(10, 2)
                    .HasColumnName("hall_width");

                entity.Property(e => e.IsElecOnCeiling).HasColumnName("is_elec_on_ceiling");

                entity.Property(e => e.IsElecOnFloor).HasColumnName("is_elec_on_floor");

                entity.Property(e => e.LoadingDocks).HasColumnName("loading_docks");

                entity.Property(e => e.LocationId).HasColumnName("location_id");

                entity.Property(e => e.OverheadHeight)
                    .HasPrecision(10, 2)
                    .HasColumnName("overhead_height");

                entity.HasOne(d => d.Location)
                    .WithMany(p => p.ShowLocationHalls)
                    .HasForeignKey(d => d.LocationId)
                    .HasConstraintName("fk_location");
            });

            modelBuilder.Entity<ShowLocations>(entity =>
            {
                entity.HasKey(e => e.LocationId)
                    .HasName("show_locations_pkey");

                entity.ToTable("show_locations");

                entity.Property(e => e.LocationId).HasColumnName("location_id");

                entity.Property(e => e.LocationAccessplan)
                    .HasMaxLength(255)
                    .HasColumnName("location_accessplan");

                entity.Property(e => e.LocationAddress1)
                    .HasMaxLength(100)
                    .HasColumnName("location_address1");

                entity.Property(e => e.LocationAddress2)
                    .HasMaxLength(100)
                    .HasColumnName("location_address2");

                entity.Property(e => e.LocationCity)
                    .HasMaxLength(100)
                    .HasColumnName("location_city");

                entity.Property(e => e.LocationCode)
                    .IsRequired()
                    .HasMaxLength(10)
                    .HasColumnName("location_code");

                entity.Property(e => e.LocationCountryId).HasColumnName("location_country_id");

                entity.Property(e => e.LocationEmail)
                    .HasMaxLength(100)
                    .HasColumnName("location_email");

                entity.Property(e => e.LocationFax)
                    .HasMaxLength(20)
                    .HasColumnName("location_fax");

                entity.Property(e => e.LocationMapLink)
                    .HasMaxLength(255)
                    .HasColumnName("location_map_link");

                entity.Property(e => e.LocationName)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("location_name");

                entity.Property(e => e.LocationPostalCode)
                    .HasMaxLength(20)
                    .HasColumnName("location_postal_code");

                entity.Property(e => e.LocationProvinceId).HasColumnName("location_province_id");

                entity.Property(e => e.LocationShippingAddress1)
                    .HasMaxLength(100)
                    .HasColumnName("location_shipping_address1");

                entity.Property(e => e.LocationShippingAddress2)
                    .HasMaxLength(100)
                    .HasColumnName("location_shipping_address2");

                entity.Property(e => e.LocationShippingCity)
                    .HasMaxLength(100)
                    .HasColumnName("location_shipping_city");

                entity.Property(e => e.LocationShippingPostalcode)
                    .HasMaxLength(20)
                    .HasColumnName("location_shipping_postalcode");

                entity.Property(e => e.LocationTelephone)
                    .HasMaxLength(20)
                    .HasColumnName("location_telephone");

                entity.Property(e => e.LocationTollfree)
                    .HasMaxLength(20)
                    .HasColumnName("location_tollfree");

                entity.Property(e => e.LocationWebsite)
                    .HasMaxLength(100)
                    .HasColumnName("location_website");

                entity.Property(e => e.ShippingCountryId).HasColumnName("shipping_country_id");

                entity.Property(e => e.ShippingProvinceId).HasColumnName("shipping_province_id");

                entity.HasOne(d => d.LocationCountry)
                    .WithMany(p => p.ShowLocationsLocationCountry)
                    .HasForeignKey(d => d.LocationCountryId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_location_country");

                entity.HasOne(d => d.LocationProvince)
                    .WithMany(p => p.ShowLocationsLocationProvince)
                    .HasForeignKey(d => d.LocationProvinceId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_location_province");

                entity.HasOne(d => d.ShippingCountry)
                    .WithMany(p => p.ShowLocationsShippingCountry)
                    .HasForeignKey(d => d.ShippingCountryId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_shipping_country");

                entity.HasOne(d => d.ShippingProvince)
                    .WithMany(p => p.ShowLocationsShippingProvince)
                    .HasForeignKey(d => d.ShippingProvinceId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_shipping_province");
            });

            modelBuilder.Entity<TaxProvince>(entity =>
            {
                entity.ToTable("tax_province");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("created_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.Property(e => e.CreatedById).HasColumnName("created_by_id");

                entity.Property(e => e.DisplayOrder)
                    .HasColumnName("display_order")
                    .HasDefaultValueSql("1");

                entity.Property(e => e.IsActive)
                    .HasColumnName("is_active")
                    .HasDefaultValueSql("true");

                entity.Property(e => e.ProvinceId).HasColumnName("province_id");

                entity.Property(e => e.TaxRate)
                    .HasPrecision(6, 2)
                    .HasColumnName("tax_rate");

                entity.Property(e => e.TaxTypeId).HasColumnName("tax_type_id");

                entity.Property(e => e.UpdatedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("updated_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.Property(e => e.UpdatedById).HasColumnName("updated_by_id");

                entity.Property(e => e.UpdatedIsActiveById).HasColumnName("updated_is_active_by_id");

                entity.Property(e => e.UpdatedIsActiveDate)
                    .HasColumnType("timestamp without time zone")
                    .HasColumnName("updated_is_active_date")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.HasOne(d => d.CreatedBy)
                    .WithMany(p => p.TaxProvinceCreatedBy)
                    .HasForeignKey(d => d.CreatedById)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_tax_province_created_by");

                entity.HasOne(d => d.Province)
                    .WithMany(p => p.TaxProvince)
                    .HasForeignKey(d => d.ProvinceId)
                    .HasConstraintName("fk_province");

                entity.HasOne(d => d.TaxType)
                    .WithMany(p => p.TaxProvince)
                    .HasForeignKey(d => d.TaxTypeId)
                    .HasConstraintName("fk_tax_type");

                entity.HasOne(d => d.UpdatedBy)
                    .WithMany(p => p.TaxProvinceUpdatedBy)
                    .HasForeignKey(d => d.UpdatedById)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_tax_province_updated_by");

                entity.HasOne(d => d.UpdatedIsActiveBy)
                    .WithMany(p => p.TaxProvinceUpdatedIsActiveBy)
                    .HasForeignKey(d => d.UpdatedIsActiveById)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("fk_tax_province_updated_is_active_by");
            });

            modelBuilder.Entity<TaxType>(entity =>
            {
                entity.ToTable("tax_type");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.TaxAbr)
                    .HasMaxLength(6)
                    .HasColumnName("tax_abr");

                entity.Property(e => e.TaxName)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("tax_name");
            });

            modelBuilder.HasSequence("Auth_Permission_id_seq");

            modelBuilder.HasSequence("Auth_Role_id_seq");

            modelBuilder.HasSequence("Auth_User_id_seq");

            modelBuilder.HasSequence("Menu_Item_MenuItemID_seq");

            modelBuilder.HasSequence("Menu_Section_Id_seq");

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}