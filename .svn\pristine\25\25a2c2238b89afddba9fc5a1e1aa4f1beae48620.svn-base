﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Models
{
    [Table("show_locations")]
    public partial class ShowLocation
    {
        public ShowLocation()
        {
            ShowLocationContacts = new HashSet<ShowLocationContact>();
            ShowLocationHalls = new HashSet<ShowLocationHall>();
        }

        [Key]
        [Column("location_id")]
        public int LocationId { get; set; }
        [Required]
        [Column("location_code")]
        [StringLength(10)]
        public string LocationCode { get; set; }
        [Required]
        [Column("location_name")]
        [StringLength(100)]
        public string LocationName { get; set; }
        [Column("location_address1")]
        [StringLength(100)]
        public string LocationAddress1 { get; set; }
        [Column("location_address2")]
        [StringLength(100)]
        public string LocationAddress2 { get; set; }
        [Column("location_postal_code")]
        [StringLength(20)]
        public string LocationPostalCode { get; set; }
        [Column("location_city")]
        [StringLength(100)]
        public string LocationCity { get; set; }
        [Column("location_province_id")]
        public int? LocationProvinceId { get; set; }
        [Column("location_country_id")]
        public int? LocationCountryId { get; set; }
        [Column("location_telephone")]
        [StringLength(20)]
        public string LocationTelephone { get; set; }
        [Column("location_tollfree")]
        [StringLength(20)]
        public string LocationTollfree { get; set; }
        [Column("location_fax")]
        [StringLength(20)]
        public string LocationFax { get; set; }
        [Column("location_map_link")]
        [StringLength(255)]
        public string LocationMapLink { get; set; }
        [Column("location_website")]
        [StringLength(100)]
        public string LocationWebsite { get; set; }
        [Column("location_email")]
        [StringLength(100)]
        public string LocationEmail { get; set; }
        [Column("location_accessplan")]
        [StringLength(255)]
        public string LocationAccessplan { get; set; }
        [Column("location_shipping_address1")]
        [StringLength(100)]
        public string LocationShippingAddress1 { get; set; }
        [Column("location_shipping_address2")]
        [StringLength(100)]
        public string LocationShippingAddress2 { get; set; }
        [Column("location_shipping_postalcode")]
        [StringLength(20)]
        public string LocationShippingPostalcode { get; set; }
        [Column("location_shipping_city")]
        [StringLength(100)]
        public string LocationShippingCity { get; set; }
        [Column("shipping_country_id")]
        public int? ShippingCountryId { get; set; }
        [Column("shipping_province_id")]
        public int? ShippingProvinceId { get; set; }

        [ForeignKey("LocationCountryId")]
        [InverseProperty("ShowLocationLocationCountries")]
        public virtual Country LocationCountry { get; set; }
        [ForeignKey("LocationProvinceId")]
        [InverseProperty("ShowLocationLocationProvinces")]
        public virtual Province LocationProvince { get; set; }
        [ForeignKey("ShippingCountryId")]
        [InverseProperty("ShowLocationShippingCountries")]
        public virtual Country ShippingCountry { get; set; }
        [ForeignKey("ShippingProvinceId")]
        [InverseProperty("ShowLocationShippingProvinces")]
        public virtual Province ShippingProvince { get; set; }
        [InverseProperty("Location")]
        public virtual ICollection<ShowLocationContact> ShowLocationContacts { get; set; }
        [InverseProperty("Location")]
        public virtual ICollection<ShowLocationHall> ShowLocationHalls { get; set; }
    }
}