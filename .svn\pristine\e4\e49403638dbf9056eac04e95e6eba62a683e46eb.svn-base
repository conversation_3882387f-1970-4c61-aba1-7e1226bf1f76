﻿namespace goodkey_cms.DTO.Menu
{
    public class MenuBasicDetail : BasicDetail
    {
        public string? Icon { get; set; }
        public bool IsVisible { get; set; }
        public bool IsDashboard { get; set; }
        public string? PermissionKey { get; set; }
        public int? DisplayOrder { get; set; }
        public MenuBasicDetail? Parent { get; set; }
        public string? Url { get; set; }
        public string? Target { get; set; }
        public bool IsParent { get; set; }
        public string? Image { get; set; }
        public string? Description { get; set; }
        public string? Section { get; set; }
        public IEnumerable<MenuBasicDetail> Children { get; set; }

    }
    public class MenuConfig : BasicDetail
    {
        public bool HasImage { get; set; }
        public bool HasIcon { get; set; }
        public bool HasDescription { get; set; }
        public bool IsPermissionBased { get; set; }
        public bool IsRoleBased { get; set; }
        public bool IsLevelBased { get; set; }

    }
    public class MenuDetail : MenuBasicDetail
    {


    }
    public class SectionDetail : BasicDetail
    {
        public bool IsDashboard { get; set; }
    }
}
