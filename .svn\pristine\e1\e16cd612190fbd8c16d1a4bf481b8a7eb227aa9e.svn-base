﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class AuthGroup
    {
        public AuthGroup()
        {
            AuthGroupRole = new HashSet<AuthGroupRole>();
            AuthUser = new HashSet<AuthUser>();
        }

        public int GroupId { get; set; }
        public string GroupName { get; set; }
        public int? MinLevel { get; set; }
        public int? MaxLevel { get; set; }

        public virtual ICollection<AuthGroupRole> AuthGroupRole { get; set; }
        public virtual ICollection<AuthUser> AuthUser { get; set; }
    }
}