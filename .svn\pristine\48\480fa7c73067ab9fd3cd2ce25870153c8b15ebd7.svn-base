﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class Warehouse
    {
        public int WarehouseId { get; set; }
        public string Code { get; set; }
        public string WarehouseName { get; set; }
        public string AddressLine1 { get; set; }
        public string AddressLine2 { get; set; }
        public string City { get; set; }
        public string PostalCode { get; set; }
        public int? ProvinceId { get; set; }
        public int? CountryId { get; set; }
        public int? WarehouseTypeId { get; set; }
        public int? ContactPersonId { get; set; }
        public DateTime CreatedAt { get; set; }
        public int? CreatedById { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int? UpdatedById { get; set; }
        public bool? IsActive { get; set; }
        public int? LastUpdateActiveById { get; set; }
        public DateTime? LastUpdatedActiveAt { get; set; }

        public virtual AuthUser ContactPerson { get; set; }
        public virtual Countries Country { get; set; }
        public virtual AuthUser CreatedBy { get; set; }
        public virtual AuthUser LastUpdateActiveBy { get; set; }
        public virtual Provinces Province { get; set; }
        public virtual AuthUser UpdatedBy { get; set; }
        public virtual WarehouseTypes WarehouseType { get; set; }
    }
}