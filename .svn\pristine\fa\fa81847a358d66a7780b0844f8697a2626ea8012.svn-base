﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Models
{
    [Table("auth_permission")]
    public partial class AuthPermission
    {
        public AuthPermission()
        {
            Roles = new HashSet<AuthRole>();
        }

        [Key]
        [Column("permission_id")]
        public int PermissionId { get; set; }
        [Required]
        [Column("name")]
        [StringLength(255)]
        public string Name { get; set; }
        [Required]
        [Column("code")]
        [StringLength(255)]
        public string Code { get; set; }

        [ForeignKey("PermissionId")]
        [InverseProperty("Permissions")]
        public virtual ICollection<AuthRole> Roles { get; set; }
    }
}