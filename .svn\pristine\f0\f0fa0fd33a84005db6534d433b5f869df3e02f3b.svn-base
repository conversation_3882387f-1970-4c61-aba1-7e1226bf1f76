﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Models
{
    [Table("menu_item")]
    public partial class MenuItem
    {
        public MenuItem()
        {
            InverseParent = new HashSet<MenuItem>();
            Roles = new HashSet<AuthRole>();
        }

        [Key]
        [Column("menu_item_id")]
        public int MenuItemId { get; set; }
        [Column("parent_id")]
        public int? ParentId { get; set; }
        [Column("url")]
        public string Url { get; set; }
        [Column("display_order")]
        public int DisplayOrder { get; set; }
        [Column("target")]
        public string Target { get; set; }
        [Column("is_visible")]
        public bool? IsVisible { get; set; }
        [Column("icon_name")]
        public string IconName { get; set; }
        [Column("image_path")]
        public string ImagePath { get; set; }
        [Column("level")]
        public int? Level { get; set; }
        [Column("is_parent")]
        public bool? IsParent { get; set; }
        [Column("is_dashboard")]
        public bool? IsDashboard { get; set; }
        [Column("is_static")]
        public bool? IsStatic { get; set; }
        [Column("direction")]
        [StringLength(3)]
        public string Direction { get; set; }
        [Column("section_id")]
        public int? SectionId { get; set; }
        [Column("menu_id")]
        public int? MenuId { get; set; }
        [Column("permission_key")]
        [StringLength(255)]
        public string PermissionKey { get; set; }
        [Column("name")]
        public string Name { get; set; }
        [Column("meta_description")]
        public string MetaDescription { get; set; }
        [Column("meta_keywords")]
        public string MetaKeywords { get; set; }
        [Column("description")]
        public string Description { get; set; }

        [ForeignKey("MenuId")]
        [InverseProperty("MenuItems")]
        public virtual Menu Menu { get; set; }
        [ForeignKey("ParentId")]
        [InverseProperty("InverseParent")]
        public virtual MenuItem Parent { get; set; }
        [ForeignKey("SectionId")]
        [InverseProperty("MenuItems")]
        public virtual MenuSection Section { get; set; }
        [InverseProperty("Parent")]
        public virtual ICollection<MenuItem> InverseParent { get; set; }

        [ForeignKey("MenuItemId")]
        [InverseProperty("MenuItems")]
        public virtual ICollection<AuthRole> Roles { get; set; }
    }
}