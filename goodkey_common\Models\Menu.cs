﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class Menu
    {
        public Menu()
        {
            MenuItem = new HashSet<MenuItem>();
            Role = new HashSet<AuthRole>();
        }

        public int Id { get; set; }

        public virtual ICollection<MenuItem> MenuItem { get; set; }

        public virtual ICollection<AuthRole> Role { get; set; }
    }
}