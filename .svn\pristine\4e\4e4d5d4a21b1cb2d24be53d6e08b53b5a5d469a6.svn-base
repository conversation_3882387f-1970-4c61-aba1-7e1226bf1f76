﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_cms.Repositories
{
    public interface ICompanyRepository
    {
        IEnumerable<Company> GetAll();
        Company? Get(int id);
        bool Add(string name, string phone, string address, string postalCode, string city, int provinceId, int countryId, string websiteUrl, string username);
        bool Update(int id, string name, string phone, string address, string postalCode, string city, int provinceId, int countryId, string websiteUrl, string username);
    }

    public class CompanyRepository : ICompanyRepository
    {
        private readonly GoodkeyContext _context;

        public CompanyRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public bool Add(string name, string phone, string address, string postalCode, string city, int provinceId, int countryId, string websiteUrl, string username)
        {
            AuthUser? user = _context.AuthUser.FirstOrDefault(x => x.Username == username);

            if (user == null)
            {
                return false;
            }

            var existingCompany = _context.Company.FirstOrDefault(d => d.CompanyName == name);
            if (existingCompany != null)
            {
                return false;
            }

            var department = new Company()
            {
                CompanyName = name,
                Phone = phone,
                City = city,
                Street = address,
                ProvinceId = provinceId,
                CountryId = countryId,
                PostalCode = postalCode,
                WebsiteUrl = websiteUrl,
                CreatedById = user.UserId,
                CreatedAt = DateTime.Now,
            };

            _context.Company.Add(department);
            _context.SaveChanges();
            return true;
        }

        public Company? Get(int id)
        {
            return _context.Company.Include(x => x.Province).Include(x => x.Country).FirstOrDefault(x => x.CompanyId == id);
        }

        public IEnumerable<Company> GetAll()
        {
            return _context.Company.Include(x => x.Province).Include(x => x.Country);
        }

        public bool Update(int id, string name, string phone, string address, string postalCode, string city, int provinceId, int countryId, string websiteUrl, string username)
        {
            AuthUser? user = _context.AuthUser.FirstOrDefault(x => x.Username == username);
            var company = _context.Company.FirstOrDefault(x => x.CompanyId == id);

            if (company == null || user == null)
            {
                return false;
            }

            int userId = user.UserId;

            company.CompanyName = name;
            company.Street = address;
            company.City = city;
            company.PostalCode = postalCode;
            company.ProvinceId = provinceId;
            company.CountryId = countryId;
            company.WebsiteUrl = websiteUrl;
            company.Phone = phone;
            company.Updatedat = DateTime.Now;
            company.Updatedbyid = user.UserId;

            _context.Company.Update(company);
            _context.SaveChanges();
            return true;
        }
    }
}
