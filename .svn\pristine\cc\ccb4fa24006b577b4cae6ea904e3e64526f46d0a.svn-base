﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class ShowLocations
    {
        public ShowLocations()
        {
            ShowLocationContacts = new HashSet<ShowLocationContacts>();
            ShowLocationHalls = new HashSet<ShowLocationHalls>();
        }

        public int LocationId { get; set; }
        public string LocationCode { get; set; }
        public string LocationName { get; set; }
        public string LocationAddress1 { get; set; }
        public string LocationAddress2 { get; set; }
        public string LocationPostalCode { get; set; }
        public string LocationCity { get; set; }
        public int? LocationProvinceId { get; set; }
        public int? LocationCountryId { get; set; }
        public string LocationTelephone { get; set; }
        public string LocationTollfree { get; set; }
        public string LocationFax { get; set; }
        public string LocationMapLink { get; set; }
        public string LocationWebsite { get; set; }
        public string LocationEmail { get; set; }
        public string LocationAccessplan { get; set; }
        public string LocationShippingAddress1 { get; set; }
        public string LocationShippingAddress2 { get; set; }
        public string LocationShippingPostalcode { get; set; }
        public string LocationShippingCity { get; set; }
        public int? ShippingCountryId { get; set; }
        public int? ShippingProvinceId { get; set; }

        public virtual Countries LocationCountry { get; set; }
        public virtual Provinces LocationProvince { get; set; }
        public virtual Countries ShippingCountry { get; set; }
        public virtual Provinces ShippingProvince { get; set; }
        public virtual ICollection<ShowLocationContacts> ShowLocationContacts { get; set; }
        public virtual ICollection<ShowLocationHalls> ShowLocationHalls { get; set; }
    }
}