﻿using HandlebarsDotNet;

namespace goodkey_cms.Infrastructure.Helpers
{
    public class EmailHelper
    {
        private static readonly string Wrapper = "<!DOCTYPE html>\r\n<html lang=\"{{langCode}}\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n</head>\r\n<body style=\"margin: 0; font-family: Arial, sans-serif;\">\r\n\r\n    <table role=\"presentation\" width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\">\r\n        <tr>\r\n            <td align=\"center\" style=\"padding: 0 50px;\">\r\n                <svg viewBox=\"0 0 617 147\" fill=\"none\" width=\"20%\" height=\"100%\">\r\n                    <path d=\"M92 32H138V43H92V32Z\" fill=\"#E0201A\"></path>\r\n                    <path d=\"M92 57H138V68H92V57Z\" fill=\"#E0201A\"></path>\r\n                    <path d=\"M92 82H138V92H92V82Z\" fill=\"#E0201A\"></path>\r\n                    <path d=\"M69.72 33.368V92H57.96V53.864L42.252 92H33.348L17.556 53.864V92H5.796V33.368H19.152L37.8 76.964L56.448 33.368H69.72ZM201.234 33.368V42.86H185.61V92H173.85V42.86H158.226V33.368H201.234ZM263.406 80.828H240.054L236.19 92H223.842L244.926 33.284H258.618L279.702 92H267.27L263.406 80.828ZM260.214 71.42L251.73 46.892L243.246 71.42H260.214ZM316.983 82.676H336.303V92H305.223V33.368H316.983V82.676ZM373.01 33.368V69.656C373.01 73.632 374.046 76.684 376.118 78.812C378.19 80.884 381.102 81.92 384.854 81.92C388.662 81.92 391.602 80.884 393.674 78.812C395.746 76.684 396.782 73.632 396.782 69.656V33.368H408.626V69.572C408.626 74.556 407.534 78.784 405.35 82.256C403.222 85.672 400.338 88.248 396.698 89.984C393.114 91.72 389.11 92.588 384.686 92.588C380.318 92.588 376.342 91.72 372.758 89.984C369.23 88.248 366.43 85.672 364.358 82.256C362.286 78.784 361.25 74.556 361.25 69.572V33.368H373.01ZM487.776 92H476.016L449.388 51.764V92H437.628V33.284H449.388L476.016 73.604V33.284H487.776V92ZM528.798 33.368V92H517.038V33.368H528.798ZM555.201 62.6C555.201 56.832 556.489 51.68 559.065 47.144C561.697 42.552 565.253 38.996 569.733 36.476C574.269 33.9 579.337 32.612 584.937 32.612C591.489 32.612 597.229 34.292 602.157 37.652C607.085 41.012 610.529 45.66 612.489 51.596H598.965C597.621 48.796 595.717 46.696 593.253 45.296C590.845 43.896 588.045 43.196 584.853 43.196C581.437 43.196 578.385 44.008 575.697 45.632C573.065 47.2 570.993 49.44 569.481 52.352C568.025 55.264 567.297 58.68 567.297 62.6C567.297 66.464 568.025 69.88 569.481 72.848C570.993 75.76 573.065 78.028 575.697 79.652C578.385 81.22 581.437 82.004 584.853 82.004C588.045 82.004 590.845 81.304 593.253 79.904C595.717 78.448 597.621 76.32 598.965 73.52H612.489C610.529 79.512 607.085 84.188 602.157 87.548C597.285 90.852 591.545 92.504 584.937 92.504C579.337 92.504 574.269 91.244 569.733 88.724C565.253 86.148 561.697 82.592 559.065 78.056C556.489 73.52 555.201 68.368 555.201 62.6Z\" fill=\"black\"></path>\r\n                </svg>\r\n                <table role=\"presentation\" width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" style=\"background: white; box-shadow: 0px 0px 5px gray;\">\r\n                    <tr>\r\n                        <td style=\"padding: 50px 70px;\">\r\n{{{content}}}\r\n                        </td>\r\n                    </tr>\r\n                </table>\r\n            </td>\r\n        </tr>\r\n    </table>\r\n\r\n</body>\r\n</html>";
        public static string generateEmail(string content, string culture = "en")
        {
            var wrapper = Handlebars.Compile(Wrapper);
            return wrapper(new { langCode = culture, content });
        }
    }
}
