﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class MenuItem
    {
        public MenuItem()
        {
            InverseParent = new HashSet<MenuItem>();
            Role = new HashSet<AuthRole>();
        }

        public int MenuItemId { get; set; }
        public int? ParentId { get; set; }
        public string Url { get; set; }
        public int DisplayOrder { get; set; }
        public string Target { get; set; }
        public bool? IsVisible { get; set; }
        public string IconName { get; set; }
        public string ImagePath { get; set; }
        public int? Level { get; set; }
        public bool? IsParent { get; set; }
        public bool? IsDashboard { get; set; }
        public bool? IsStatic { get; set; }
        public string Direction { get; set; }
        public int? SectionId { get; set; }
        public int? MenuId { get; set; }
        public string PermissionKey { get; set; }
        public string Name { get; set; }
        public string MetaDescription { get; set; }
        public string MetaKeywords { get; set; }
        public string Description { get; set; }

        public virtual Menu Menu { get; set; }
        public virtual MenuItem Parent { get; set; }
        public virtual MenuSection Section { get; set; }
        public virtual ICollection<MenuItem> InverseParent { get; set; }

        public virtual ICollection<AuthRole> Role { get; set; }
    }
}