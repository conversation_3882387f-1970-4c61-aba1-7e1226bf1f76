﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{
    public interface ITaxRepository
    {
        IEnumerable<TaxType> GetTaxTypes();
        IEnumerable<TaxProvince> GetTaxProvince();
        TaxProvince? GetTaxProvince(int id);
        bool CreateTaxProvince(int taxTypeId, int provinceId, int displayOrder, decimal taxRate, int userId);
        bool UpdateTaxProvince(int id, int displayOrder, decimal taxRate, int userId);
        bool UpdateIsActive(int id, bool isActive, int currentUserId);
    }

    public class TaxRepository : ITaxRepository
    {
        private readonly GoodkeyContext _context;

        public TaxRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public bool CreateTaxProvince(int taxTypeId, int provinceId, int displayOrder, decimal taxRate, int userId)
        {
            var taxType = _context.TaxType.FirstOrDefault(x => x.Id == taxTypeId);

            var province = _context.Provinces.FirstOrDefault(x => x.ProvinceId == provinceId);

            if (taxType == null || province == null)
            {
                return false;
            }

            _context.Add(new TaxProvince()
            {
                TaxType = taxType,
                Province = province,
                DisplayOrder = displayOrder,
                TaxRate = taxRate,
                CreatedAt = DateTime.Now,
                CreatedById = userId
            });

            _context.SaveChanges();
            return true;
        }

        public IEnumerable<TaxProvince> GetTaxProvince()
        {
            return _context.TaxProvince.Include(x => x.Province).Include(x => x.TaxType);
        }

        public TaxProvince? GetTaxProvince(int id)
        {
            return _context.TaxProvince.Include(x => x.Province).Include(x => x.TaxType).FirstOrDefault(x => x.Id == id);
        }

        public IEnumerable<TaxType> GetTaxTypes()
        {
            return _context.TaxType;
        }

        public bool UpdateIsActive(int id, bool isActive, int currentUserId)
        {
            var taxProvince = _context.TaxProvince.FirstOrDefault(x => x.Id == id);
            if (taxProvince == null) return false;

            taxProvince.IsActive = isActive;
            taxProvince.UpdatedIsActiveDate = DateTime.Now;
            taxProvince.UpdatedIsActiveById = currentUserId;
            _context.SaveChanges();

            return true;
        }


        public bool UpdateTaxProvince(int id, int displayOrder, decimal taxRate, int userId)
        {
            var taxProvince = _context.TaxProvince.FirstOrDefault(x => x.Id == id);
            if (taxProvince == null) return false;

            taxProvince.DisplayOrder = displayOrder;
            taxProvince.TaxRate = taxRate;
            taxProvince.UpdatedById = userId;
            taxProvince.UpdatedAt = DateTime.Now;
            _context.SaveChanges();

            return true;
        }
    }
}
