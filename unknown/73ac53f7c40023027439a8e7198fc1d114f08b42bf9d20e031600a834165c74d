﻿using MailKit.Net.Smtp;
using MailKit.Security;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using MimeKit;

namespace goodkey_common.Services
{

    public class MailSettings
    {
        public string Server { get; set; }
        public int Port { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
    }
    public interface IMailService
    {
        Task SendAsync(string recipient, string subject, string message);
        void Send(string recipient, string subject, string message, List<IFormFile>? attachments = null, bool isHtml = true);
        void Send(string recipient, string subject, string message, string sender);
        void SendToMultipleRecipients(string recipient, string subject, string message, List<IFormFile>? attachments = null, bool isHtml = true);
    }

    public class MailService : IMailService
    {
        private readonly MailSettings _mailSettings;

        public MailService(IOptions<MailSettings> mailSettingsOptions)
        {
            _mailSettings = mailSettingsOptions.Value;
        }

        public async Task SendAsync(string recipient, string subject, string message)
        {
            var emailMessage = CreateEmailMessage(recipient, subject, message);

            using var client = new SmtpClient();
            await client.ConnectAsync(_mailSettings.Server, _mailSettings.Port, SecureSocketOptions.Auto);
            await client.AuthenticateAsync(_mailSettings.Username, _mailSettings.Password);
            await client.SendAsync(emailMessage);
            await client.DisconnectAsync(true);
        }

        public void Send(string recipient, string subject, string message, List<IFormFile>? attachments = null, bool isHtml = true)

        {
            var emailMessage = CreateEmailMessage(recipient, subject, message);
            if (attachments != null && attachments.Any())
            {
                if (attachments != null && attachments.Any())
                {
                    var multipart = new Multipart("mixed");

                    foreach (var attachment in attachments)
                    {
                        if (attachment.Length > 0)
                        {
                            var attachmentStream = new MemoryStream();
                            attachment.CopyTo(attachmentStream);
                            attachmentStream.Seek(0, SeekOrigin.Begin);

                            var mimeAttachment = new MimePart()
                            {
                                Content = new MimeContent(attachmentStream),
                                ContentDisposition = new ContentDisposition(ContentDisposition.Attachment),
                                ContentTransferEncoding = ContentEncoding.Base64,
                                FileName = attachment.FileName
                            };

                            multipart.Add(mimeAttachment);
                        }
                    }

                    multipart.Add(new TextPart(isHtml ? "html" : "plain")
                    {
                        Text = message
                    });

                    emailMessage.Body = multipart;
                }

            }
            using var client = new SmtpClient();
            client.Connect(_mailSettings.Server, _mailSettings.Port, SecureSocketOptions.Auto);
            client.Authenticate(_mailSettings.Username, _mailSettings.Password);
            client.Send(emailMessage);
            client.Disconnect(true);
        }

        public void Send(string recipient, string subject, string message, string sender)
        {
            var emailMessage = CreateEmailMessage(recipient, subject, message, sender);

            using var client = new SmtpClient();
            client.Connect(_mailSettings.Server, _mailSettings.Port, SecureSocketOptions.Auto);
            client.Authenticate(_mailSettings.Username, _mailSettings.Password);
            client.Send(emailMessage);
            client.Disconnect(true);
        }

        public void SendToMultipleRecipients(string recipient, string subject, string message, List<IFormFile>? attachments = null, bool isHtml = true)

        {
            var emailMessage = CreateEmailMessageForMultipleRecepients(recipient, subject, message);
            if (attachments != null && attachments.Any())
            {
                if (attachments != null && attachments.Any())
                {
                    var multipart = new Multipart("mixed");

                    foreach (var attachment in attachments)
                    {
                        if (attachment.Length > 0)
                        {
                            var attachmentStream = new MemoryStream();
                            attachment.CopyTo(attachmentStream);
                            attachmentStream.Seek(0, SeekOrigin.Begin);

                            var mimeAttachment = new MimePart()
                            {
                                Content = new MimeContent(attachmentStream),
                                ContentDisposition = new ContentDisposition(ContentDisposition.Attachment),
                                ContentTransferEncoding = ContentEncoding.Base64,
                                FileName = attachment.FileName
                            };

                            multipart.Add(mimeAttachment);
                        }
                    }

                    multipart.Add(new TextPart(isHtml ? "html" : "plain")
                    {
                        Text = message
                    });

                    emailMessage.Body = multipart;
                }

            }
            using var client = new SmtpClient();
            client.Connect(_mailSettings.Server, _mailSettings.Port, SecureSocketOptions.Auto);
            client.Authenticate(_mailSettings.Username, _mailSettings.Password);
            client.Send(emailMessage);
            client.Disconnect(true);
        }


        private MimeMessage CreateEmailMessage(string recipient, string subject, string message)
        {
            var emailMessage = new MimeMessage();
            emailMessage.From.Add(MailboxAddress.Parse(_mailSettings.Username));
            emailMessage.To.Add(MailboxAddress.Parse(recipient));
            emailMessage.Subject = subject;

            var bodyBuilder = new BodyBuilder
            {
                HtmlBody = message
            };
            emailMessage.Body = bodyBuilder.ToMessageBody();

            return emailMessage;
        }
        private MimeMessage CreateEmailMessageForMultipleRecepients(string recipient, string subject, string message)
        {
            var emailMessage = new MimeMessage();
            emailMessage.From.Add(MailboxAddress.Parse(_mailSettings.Username));

            // Split by comma and add each recipient separately
            var recipients = recipient.Split(',')
                                      .Select(r => r.Trim())
                                      .Where(r => !string.IsNullOrEmpty(r));
            foreach (var r in recipients)
            {
                emailMessage.To.Add(MailboxAddress.Parse(r));
            }

            emailMessage.Subject = subject;

            var bodyBuilder = new BodyBuilder
            {
                HtmlBody = message
            };
            emailMessage.Body = bodyBuilder.ToMessageBody();

            return emailMessage;
        }


        private MimeMessage CreateEmailMessage(string recipient, string subject, string message, string sender)
        {
            var emailMessage = new MimeMessage();
            emailMessage.From.Add(MailboxAddress.Parse(sender));
            emailMessage.To.Add(MailboxAddress.Parse(recipient));
            emailMessage.Subject = subject;

            var bodyBuilder = new BodyBuilder
            {
                HtmlBody = message,
            };
            emailMessage.Body = bodyBuilder.ToMessageBody();

            return emailMessage;
        }
    }
}