using goodkey_cms.DTO.Role;

namespace goodkey_cms.DTO.RoleGroup
{
    public class RoleGroupDetail
    {
        public int Id { get; set; }
        public string Name { get; set; } = null!;
        public int MinLevel { get; set; }
        public int MaxLevel { get; set; }
        public IEnumerable<BasicRoleDetail>? Roles { get; set; }
    }

    public class BasicRoleGroupDetail
    {
        public int Id { get; set; }
        public string Name { get; set; } = null!;
        public int MinLevel { get; set; }
        public int MaxLevel { get; set; }
        public IEnumerable<BasicRoleDetail>? Roles { get; set; }
    }
}
