﻿using goodkey_common.Context;
using goodkey_common.Models;

namespace goodkey_common.Repositories
{
    public interface ICountryRepository
    {
        IEnumerable<Countries> GetAll();
        int? FindCountry(string country);
    }

    public class CountryRepository : ICountryRepository
    {
        private readonly GoodkeyContext _context;

        public CountryRepository(GoodkeyContext context)
        {
            _context = context;
        }
        public IEnumerable<Countries> GetAll()
        {
            return _context.Countries;
        }


        public int? FindCountry(string country)
        {
            int? CountryCode = 0;

            CountryCode = _context.Countries.FirstOrDefault(x => x.CountryCode.Trim().ToLower() == country)?.CountryId;
            if (CountryCode == 0)
            {
                CountryCode = _context.Countries.FirstOrDefault(x => x.CountryName.Trim().ToLower() == country || x.CountryNameFr.Trim().ToLower() == country)?.CountryId;
            }

            return CountryCode;
        }

    }
}

