{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\NewGoodkey\\GoodkeyAPI\\GoodkeyAPI\\GoodkeyAPI.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\NewGoodkey\\GoodkeyAPI\\GoodkeyAPI\\GoodkeyAPI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\NewGoodkey\\GoodkeyAPI\\GoodkeyAPI\\GoodkeyAPI.csproj", "projectName": "GoodkeyAPI", "projectPath": "C:\\Users\\<USER>\\source\\repos\\NewGoodkey\\GoodkeyAPI\\GoodkeyAPI\\GoodkeyAPI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\NewGoodkey\\GoodkeyAPI\\GoodkeyAPI\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.telerik.com/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[7.0.4, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.4.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.202\\RuntimeIdentifierGraph.json"}}}}}