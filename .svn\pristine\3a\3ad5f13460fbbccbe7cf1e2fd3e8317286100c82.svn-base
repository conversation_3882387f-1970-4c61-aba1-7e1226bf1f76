﻿using goodkey_cms.DTO;
using goodkey_common.DTO;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class WarehouseTypeController : ControllerBase
    {
        private readonly WarehouseTypeRepository _repository;

        public WarehouseTypeController(WarehouseTypeRepository repository)
        {
            _repository = repository;
        }

        [HttpGet("GetAll")]
        public GenericRespond<IEnumerable<BasicDetail>> GetAll()
        {
            var warehouseTypes = _repository.GetAll()
                .Select(w => new BasicDetail
                {
                    Id = w.WarehouseTypeId,
                    Name = $"{w.Code} - {w.TypeName}"
                });

            return new GenericRespond<IEnumerable<BasicDetail>>
            {
                Data = warehouseTypes
            };
        }
    }
}
