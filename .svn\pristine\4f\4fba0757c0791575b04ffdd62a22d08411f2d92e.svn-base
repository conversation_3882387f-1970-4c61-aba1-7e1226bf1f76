﻿namespace goodkey_cms.DTO.User
{
    public class UserData
    {
        public string Email { get; set; }
        public string Password { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string? Username { get; set; }
        public int? RoleId { get; set; }
        public string? MobileNumber { get; set; }
        public string? WorkPhoneNumber { get; set; }
    }

    public class CreateUserDto
    {
        public int? StatusId { get; set; }
        // public string? PreferredLang { get; set; }
        public int? SalutationId { get; set; }

        public string FirstName { get; set; } = null!;
        public string LastName { get; set; } = null!;

        public string? Gender { get; set; }
        public DateTime? Dob { get; set; }
        public string? MobileNumber { get; set; }
        public string? PersonalEmail { get; set; }
        public string? Address { get; set; }
        public string? City { get; set; }
        public string? Province { get; set; }
        public string? PostalCode { get; set; }
        public string? EmergencyContact { get; set; }
        public string? EmergencyPhoneNumber { get; set; }
        public int? DepartmentId { get; set; }
        public string? WorkEmail { get; set; }
        public string? WorkPhoneNumber { get; set; }
    }

    public class UpdateUserDto
    {
        public int? StatusId { get; set; }
        public int? SalutationId { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Gender { get; set; }
        public DateTime? Dob { get; set; }
        public string? MobileNumber { get; set; }
        public string? PersonalEmail { get; set; }
        public string? Address { get; set; }
        public string? City { get; set; }
        public string? Province { get; set; }
        public string? PostalCode { get; set; }
        public string? EmergencyContact { get; set; }
        public string? EmergencyPhoneNumber { get; set; }
        public int? DepartmentId { get; set; }
        public string? WorkEmail { get; set; }
        public string? WorkPhoneNumber { get; set; }
    }

    public class UpdateRoleDto
    {
        public int RoleId { get; set; }
    }

    public class UpdateCurrentUserDto
    {
        public string FirstName { get; set; } = null!;
        public string LastName { get; set; } = null!;
        public string? WorkPhoneNumber { get; set; }
        public string? MobileNumber { get; set; }
    }
}
