﻿using goodkey_cms.Infrastructure.Binders;
using System.Globalization;

namespace goodkey_cms.Middlewares
{
    public class CultureMiddleware
    {
        private readonly RequestDelegate _next;

        public CultureMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Extract culture from the Accept-Language header
            var culture = GetCultureFromHeader(context.Request);

            // Set the culture for the current request
            context.SetCulture(culture);
            CultureInfo.CurrentCulture = new CultureInfo(culture);
            CultureInfo.CurrentUICulture = new CultureInfo(culture);

            await _next(context);
        }

        private string GetCultureFromHeader(HttpRequest request)
        {
            // Get the Accept-Language header value
            var acceptLanguage = request.Headers["Accept-Language"].ToString();

            // Parse the first accepted language
            var acceptedLanguages = acceptLanguage.Split(',');
            var firstLanguage = acceptedLanguages[0]?.Trim();

            // Extract the language code from the language (e.g., "en" from "en-US")
            var languageCode = firstLanguage?.Split('-')[0]?.Trim();

            return !string.IsNullOrEmpty(languageCode) ? languageCode : "en";
        }
    }
}