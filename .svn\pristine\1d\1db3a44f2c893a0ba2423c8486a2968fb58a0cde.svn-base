﻿using goodkey_cms.DTO;
using goodkey_cms.DTO.User;
using goodkey_cms.Infrastructure.Binders;
using goodkey_cms.Repositories;
using goodkey_cms.Services;
using goodkey_common.DTO;
using goodkey_common.Models;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class UsersController : ControllerBase
    {
        private readonly IUserRepository _repo;

        private readonly AuthService _authService;


        public UsersController(IUserRepository repo, AuthService authService)
        {
            _repo = repo;
            _authService = authService;
        }



        [HttpGet("[action]")]
        public GenericRespond<BriefUserData?> GetCurrent()
        {
            var username = Request.HttpContext.GetUsername();
            if (string.IsNullOrEmpty(username))
            {
                return new GenericRespond<BriefUserData?>
                {
                    Data = null,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }

            var item = _repo.GetUserByUsername(username);
            if (item == null)
            {
                return new GenericRespond<BriefUserData?>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "User not found"
                };
            }

            var email = item.VerificationEmail ?? string.Empty;

            return new GenericRespond<BriefUserData?>
            {
                Data = new BriefUserData
                {
                    Id = item.UserId,
                    Email = email,
                    IsActive = item.IsActive ?? false,
                    IsVerified = item.IsVerified ?? false,
                    Name = item.FirstName + " " + item.LastName,
                    Username = item.Username,
                    Role = item.Role == null ? null : new() { Id = item.Role.RoleId, Name = item.Role.Name },
                    WorkPhoneNumber = item.WorkPhoneNumber ?? string.Empty,
                    MobileNumber = item.MobileNumber ?? string.Empty,

                }
            };
        }

        [HttpGet]
        public GenericRespond<IEnumerable<BriefUserData>> GetAll()
        {
            var users = _repo.GetAll().Select(item =>
            {
                var brief = new BriefUserData
                {
                    Id = item.UserId,
                    Username = item.Username,
                    IsActive = item.IsActive ?? false,
                    IsVerified = item.IsVerified ?? false
                };


                brief.Name = $"{item.FirstName} {item.LastName}".Trim();
                if (string.IsNullOrWhiteSpace(brief.Name))
                {
                    brief.Name = item.Username;
                }

                brief.Email = item.VerificationEmail ?? item.WorkEmail ?? item.VerificationEmail ?? string.Empty;


                if (item.Role != null)
                {
                    brief.Role = new BasicDetail
                    {
                        Id = item.Role.RoleId,
                        Name = item.Role.Name
                    };
                }
                else
                {
                    brief.Role = new BasicDetail();
                }

                if (item.Department != null)
                {
                    brief.Note = item.Department.Name;
                }

                return brief;
            });

            return new GenericRespond<IEnumerable<BriefUserData>> { Data = users };
        }

        [HttpGet("{id:int}")]
        public GenericRespond<EmployeeDetail> GetById(int id)
        {
            var user = _repo.GetById(id);
            if (user == null)
            {
                return new GenericRespond<EmployeeDetail>
                {
                    Data = null
                };
            }

            var detail = new EmployeeDetail
            {
                Id = user.UserId,
                StatusId = user.StatusId,
                SalutationId = user.SalutationId,
                FirstName = user.FirstName,
                LastName = user.LastName,
                MobileNumber = user.MobileNumber,
                DepartmentId = user.DepartmentId,
                WorkEmail = user.WorkEmail,
                VerificationEmail = user.VerificationEmail,
                WorkPhoneNumber = user.WorkPhoneNumber,
                Archive = user.IsArchived,
                CreatedDate = user.CreatedDate,
                UpdateDate = user.UpdateDate,
                HasAccount = user.IsVerified ?? false
            };

            return new GenericRespond<EmployeeDetail>
            {
                Data = detail
            };
        }

        [HttpGet("statuses")]
        public GenericRespond<IEnumerable<BasicDetail>> GetAllStatuses()
        {

            var statuses = _repo.GetAllStatuses();

            return new GenericRespond<IEnumerable<BasicDetail>>
            {
                Data = statuses
            };
        }


        [HttpPost]
        public GenericRespond<int?> Create([FromBody] CreateUserDto dto)
        {
            var username = Request.HttpContext.GetUsername();
            var newId = _repo.CreateUser(username, dto);

            return new GenericRespond<int?>
            {
                Data = newId,
                Message = newId != null
                    ? "User  created successfully."
                    : "Failed to create user."
            };
        }



        [HttpPatch("{id:int}/role")]
        public GenericRespond<bool> UpdateRole(int id, [FromBody] UpdateRoleDto dto)
        {
            var username = Request.HttpContext.GetUsername();
            if (string.IsNullOrEmpty(username))
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }

            // Get the user to check their type
            var user = _repo.GetById(id);
            if (user == null)
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 404,
                    Message = "User not found"
                };
            }

            var success = _repo.SetRole(id, dto.RoleId, username);

            return new GenericRespond<bool>
            {
                Data = success,
                Message = success
                    ? $"User role updated successfully."
                    : $"Failed to update user role.",
                StatusCode = success ? 200 : 400
            };
        }

        [HttpPatch("[action]")]
        public GenericRespond<bool> UpdateCurrent([FromBody] UpdateCurrentUserDto dto)
        {
            var username = Request.HttpContext.GetUsername();
            if (string.IsNullOrEmpty(username))
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }

            var user = _repo.GetUserByUsername(username);
            if (user == null)
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 404,
                    Message = "User not found"
                };
            }


            bool success = _repo.UpdateCurrentUserInfo(user, dto.FirstName, dto.LastName, dto.WorkPhoneNumber, dto.MobileNumber);

            return new GenericRespond<bool>
            {
                Data = success,
                Message = success
                    ? "User information updated successfully."
                    : "Failed to update user information.",
                StatusCode = success ? 200 : 400
            };
        }

        [HttpPatch("{id:int}")]
        public GenericRespond<bool> Update(int id, [FromBody] UpdateUserDto dto)
        {
            var username = Request.HttpContext.GetUsername();
            if (string.IsNullOrEmpty(username))
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }

            // Get the user to check if it exists
            var user = _repo.GetById(id);
            if (user == null)
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 404,
                    Message = "User not found"
                };
            }

            var success = _repo.UpdateUser(id, username, dto);

            return new GenericRespond<bool>
            {
                Data = success,
                Message = success
                    ? "User updated successfully."
                    : "Failed to update user.",
                StatusCode = success ? 200 : 400
            };
        }

        [HttpPatch("{id:int}/toggle-archive")]
        public GenericRespond<bool> ToggleArchiveStatus(int id)
        {
            var username = Request.HttpContext.GetUsername();
            if (string.IsNullOrEmpty(username))
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }

            // Get the user to check if it exists
            var user = _repo.GetById(id);
            if (user == null)
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 404,
                    Message = "User not found"
                };
            }

            // Get the current archive status before toggling
            var currentlyArchived = user.IsArchived ?? false;

            var success = _repo.ToggleArchiveStatus(id, username);

            // The new status will be the opposite of the current status
            var newStatus = currentlyArchived ? "unarchived" : "archived";

            return new GenericRespond<bool>
            {
                Data = success,
                Message = success
                    ? $"User {newStatus} successfully."
                    : $"Failed to toggle archive status for user.",
                StatusCode = success ? 200 : 400
            };
        }

        [HttpPost("invite")]
        public GenericRespond<bool> InviteEmployee([FromBody] SendUserInviteDto dto)
        {
            var authUser = _repo.GetById(dto.UserId);
            if (authUser == null)
            {
                return new GenericRespond<bool> { StatusCode = 404, Message = "User not found." };
            }

            if (string.IsNullOrEmpty(authUser.VerificationEmail))
            {
                return new GenericRespond<bool> { StatusCode = 400, Message = "User has no work email." };
            }

            var verificationToken = _repo.SetInvitationTokenForUser(authUser.UserId);



            _authService.SendInviteEmail(authUser.VerificationEmail, verificationToken);
            return new GenericRespond<bool> { Data = true, Message = "Invitation sent successfully." };


        }
    }
}
