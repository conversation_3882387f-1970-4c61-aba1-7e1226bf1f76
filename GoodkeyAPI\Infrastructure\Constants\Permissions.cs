﻿namespace Goodkey_cms.Infrastructure.Constants
{
    public static class Permission
    {
        public const string ManageQuoteRequests = "manage_quote_requests";
        public const string ManageSampleRequests = "manage_sample_requests";
        public const string ManageJobApplication = "manage_job_application";
        public const string ManageContactUsRequests = "manage_contact_us_requests";
        public const string ViewSubscribersList = "view_subscribers_list";
        public const string ViewMarketing = "view_marketing";
        public const string ViewEditProduct = "view_edit_product";
        public const string ManageProducts = "manage_products";
        public const string ViewEditProject = "view_edit_project";
        public const string ManageProjects = "manage_projects";
        public const string ManageDistributors = "manage_distributors";
        public const string ViewDocumentList = "view_document_list";
        public const string ManageMasterSetup = "manage_master_setup";
        public const string ManageRoles = "manage_roles";
        public const string ManageUsers = "manage_users";
        public static readonly List<string> AllPermission = new List<string>
        {
            ManageQuoteRequests,
            ManageSampleRequests,
            ManageJobApplication,
            ManageContactUsRequests,
            ViewSubscribersList,
            ViewMarketing,
            ViewEditProduct,
            ManageProducts,
            ViewEditProject,
            ManageProjects,
            ManageDistributors,
            ViewDocumentList,
            ManageMasterSetup,
            ManageRoles,
            ManageUsers
        };

    }
}
