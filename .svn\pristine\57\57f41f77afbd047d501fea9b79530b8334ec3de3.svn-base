﻿using goodkey_cms.DTO;
using goodkey_common.DTO;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace Goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class ProvincesController : Controller
	{
		private readonly IProvinceRepository _repo;
		public ProvincesController(IProvinceRepository repo)
		{
			_repo = repo;
		}

		[HttpGet]
		public GenericRespond<IEnumerable<BasicProvince>> GetAllProvince()
		{
			return new()
			{
				Data = _repo.GetAllProvince().Select(item =>

				new BasicProvince()
				{
					Id = item.ProvinceId,
					ProvinceCode = item.ProvinceCode,
					Name = item.ProvinceName,					
					CountryName = item.Country.CountryName,
					CountryId = item.CountryId,
				})
			};
		}
	}
}
