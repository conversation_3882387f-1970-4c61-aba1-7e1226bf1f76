﻿using Newtonsoft.Json.Linq;

namespace goodkey_cms.DTO
{
    public class ExtendedDetail : BasicDetail
    {
        public string? Description { get; set; }
        public ImageDetail? Image { get; set; }
    }
    public class ImageDetail
    {
        public string? Path { get; set; }
        public string? Alt { get; set; }
        public string? Name { get; set; }
    }
    public class BasicDetail
    {
        public int? Id { get; set; }
        public string? Name { get; set; }
    }

    public class LanguageDetail : JObject
    {
    }

    public class BasicLanguageDetail
    {
        public int? Id { get; set; }
        public LanguageDetail? Name { get; set; } = new LanguageDetail();
    }

    public class DataDetail
    {
        public int? Id { get; set; }
        public string? Name { get; set; }
    }
}
