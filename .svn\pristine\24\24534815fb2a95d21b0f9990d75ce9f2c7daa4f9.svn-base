﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Models
{
    [Table("auth_status")]
    public partial class AuthStatus
    {
        public AuthStatus()
        {
            AuthUsers = new HashSet<AuthUser>();
        }

        [Key]
        [Column("status_id")]
        public int StatusId { get; set; }
        [Column("status_name")]
        [StringLength(100)]
        public string StatusName { get; set; }

        [InverseProperty("Status")]
        public virtual ICollection<AuthUser> AuthUsers { get; set; }
    }
}