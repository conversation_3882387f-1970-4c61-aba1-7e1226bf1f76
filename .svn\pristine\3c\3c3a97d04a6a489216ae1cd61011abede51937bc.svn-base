﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Models
{
    [Table("salutation")]
    public partial class Salutation
    {
        public Salutation()
        {
            AuthUsers = new HashSet<AuthUser>();
        }

        [Key]
        [Column("salutation_id")]
        public int SalutationId { get; set; }
        [Column("salutation_name")]
        [StringLength(255)]
        public string SalutationName { get; set; }

        [InverseProperty("Salutation")]
        public virtual ICollection<AuthUser> AuthUsers { get; set; }
    }
}