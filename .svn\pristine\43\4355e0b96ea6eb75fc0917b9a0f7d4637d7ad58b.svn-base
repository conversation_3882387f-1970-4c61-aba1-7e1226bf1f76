﻿using goodkey_cms.DTO;
using goodkey_cms.Repositories;
using goodkey_common.DTO;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class RolesController : ControllerBase
    {
        private readonly IRoleRepository _repo;
        public RolesController(IRoleRepository repo)
        {
            _repo = repo;
        }

        [HttpGet]
        public GenericRespond<IEnumerable<BasicDetail>> Get()
        {
            return new()
            {
                Data = _repo.GetAll().Select(item =>
                    new BasicDetail()
                    {
                        Id = item.RoleId,
                        Name = item.Name
                    }
                )
            };
        }

        [HttpGet("{id}")]
        public GenericRespond<RoleDetail?> Get(int id)
        {
            var item = _repo.Get(id);
            return new()
            {
                Data = item == null ? null :
                new RoleDetail()
                {
                    Id = item.RoleId,
                    Name = item.Name,
                    Description = item.Description,
                    Permission = item.Permission?.Select(x => x.Code)?.ToList(),
                }
            };
        }

        [HttpPost]
        public GenericRespond<int?> Create([FromBody] RoleData data)
        {
            var item = _repo.Create(data.Name, data.Description);

            if (item == null)
            {
                return new GenericRespond<int?>
                {
                    Data = null,
                    StatusCode = 400,
                    Message = "Failed to create role. A role with this name may already exist."
                };
            }

            return new GenericRespond<int?>
            {
                Data = item,
                StatusCode = 201,
                Message = "Role created successfully."
            };
        }

        [HttpPatch("{id}")]
        public GenericRespond<bool> Update(int id, [FromBody] RoleData data)
        {
            var item = _repo.Update(id, data.Name, data.Description);
            return new()
            {
                Data = item

            };
        }
        [HttpPatch("{id}/[action]")]
        public GenericRespond<bool> Permission(int id, [FromBody] PermissionData data)
        {
            var item = _repo.SetPermission(id, data.Permission);
            return new()
            {
                Data = item

            };
        }

        [HttpGet("{id}/[action]")]
        public GenericRespond<IEnumerable<int>> Menu(int id)
        {
            var item = _repo.GetMenu(id);
            return new()
            {
                Data = item
            };
        }

        [HttpPatch("{id}/[action]")]
        public GenericRespond<bool> Menu(int id, [FromBody] IEnumerable<int> data)
        {
            var item = _repo.SetMenu(id, data);
            return new()
            {
                Data = item
            };
        }
    }
}
