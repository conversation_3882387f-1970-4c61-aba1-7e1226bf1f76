﻿using goodkey_cms.DTO;
using goodkey_cms.Repositories;
using goodkey_common.DTO;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class RolesController : ControllerBase
    {
        private readonly IRoleRepository _repo;
        public RolesController(IRoleRepository repo)
        {
            _repo = repo;
        }

        [HttpGet]
        public GenericRespond<IEnumerable<BasicDetail>> Get()
        {
            return new()
            {
                Data = _repo.GetAll().Select(item =>
                    new BasicDetail()
                    {
                        Id = item.RoleId,
                        Name = item.Name
                    }
                )
            };
        }

        [HttpGet("{id}")]
        public GenericRespond<RoleDetail?> Get(int id)
        {
            var item = _repo.Get(id);
            return new()
            {
                Data = item == null ? null :
                new RoleDetail()
                {
                    Id = item.RoleId,
                    Name = item.Name,
                    Description = item.Description,
                    Permissions = item.Permissions?.Select(x => x.Code)?.ToList(),
                }
            };
        }

        [HttpPost]
        public GenericRespond<int?> Create([FromBody] RoleData data)
        {
            var item = _repo.Create(data.Name, data.Description);
            return new()
            {
                Data = item

            };
        }

        [HttpPatch("{id}")]
        public GenericRespond<bool> Update(int id, [FromBody] RoleData data)
        {
            var item = _repo.Update(id, data.Name, data.Description);
            return new()
            {
                Data = item

            };
        }
        [HttpPatch("{id}/[action]")]
        public GenericRespond<bool> Permissions(int id, [FromBody] PermissionsData data)
        {
            var item = _repo.SetPermission(id, data.Permissions);
            return new()
            {
                Data = item

            };
        }

        [HttpGet("{id}/[action]")]
        public GenericRespond<IEnumerable<int>> Menu(int id)
        {
            var item = _repo.GetMenu(id);
            return new()
            {
                Data = item
            };
        }

        [HttpPatch("{id}/[action]")]
        public GenericRespond<bool> Menu(int id, [FromBody] IEnumerable<int> data)
        {
            var item = _repo.SetMenu(id, data);
            return new()
            {
                Data = item
            };
        }
    }
}
