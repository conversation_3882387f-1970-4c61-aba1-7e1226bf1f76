﻿using goodkey_cms.DTO;
using goodkey_cms.Infrastructure.Binders;
using goodkey_cms.Repositories;
using goodkey_common.DTO;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class CompanyController : Controller
    {
        private readonly ICompanyRepository _repo;
        public CompanyController(ICompanyRepository repo)
        {
            _repo = repo;
        }

        [HttpGet]
        public GenericRespond<IEnumerable<Company>> GetAll()
        {
            var companies = _repo.GetAll();

            return new GenericRespond<IEnumerable<Company>>
            {
                Data = companies.Select(item => new Company
                {
                    Id = item.CompanyId,
                    Name = item.CompanyName,
                    Phone = item.Phone,
                    Street = item.Street,
                    City = item.City,
                    Province = item.Province.ProvinceName,
                    PostalCode = item.PostalCode,
                    Country = item.Country.CountryName,
                    WebsiteUrl = item.WebsiteUrl
                })
            };
        }

        [HttpGet("[action]/{id:int}")]
        public GenericRespond<CompanyDto> Get(int id)
        {
            var item = _repo.Get(id);
            if (item == null)
            {
                return new GenericRespond<CompanyDto>
                {
                    Data = null,
                    Message = "Company not found",
                    StatusCode = 404
                };
            }

            return new GenericRespond<CompanyDto>
            {
                Data = new CompanyDto
                {
                    Id = item.CompanyId,
                    Name = item.CompanyName,
                    Phone = item.Phone,
                    Street = item.Street,
                    City = item.City,
                    ProvinceId = item.ProvinceId,
                    PostalCode = item.PostalCode,
                    CountryId = item.CountryId,
                    WebsiteUrl = item.WebsiteUrl
                }
            };
        }

        [HttpPatch("[action]/{id:int}")]
        public GenericRespond<bool> Update(int id, CompanyDto data)
        {
            var username = Request.HttpContext.GetUsername();
            if (string.IsNullOrEmpty(username))
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    Message = "Unauthorized",
                    StatusCode = 401
                };
            }

            // Validate required fields if needed
            var success = _repo.Update(
                id,
                data.Name ?? string.Empty,
                data.Phone ?? string.Empty,
                data.Street ?? string.Empty,
                data.PostalCode ?? string.Empty,
                data.City ?? string.Empty,
                data.ProvinceId ?? 0,
                data.CountryId ?? 0,
                data.WebsiteUrl ?? string.Empty,
                username
            );

            return new GenericRespond<bool>
            {
                Data = success,
                Message = success
                    ? "Company updated successfully."
                    : "Failed to update company. It may not exist or there was a database error.",
                StatusCode = success ? 200 : 400
            };
        }

        [HttpPost("[action]")]
        public GenericRespond<bool> Add(CompanyDto data)
        {
            var username = Request.HttpContext.GetUsername();
            if (string.IsNullOrEmpty(username))
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    Message = "Unauthorized",
                    StatusCode = 401
                };
            }

            var success = _repo.Add(
                data.Name ?? string.Empty,
                data.Phone ?? string.Empty,
                data.Street ?? string.Empty,
                data.PostalCode ?? string.Empty,
                data.City ?? string.Empty,
                data.ProvinceId ?? 0,
                data.CountryId ?? 0,
                data.WebsiteUrl ?? string.Empty,
                username
            );

            return new GenericRespond<bool>
            {
                Data = success,
                Message = success
                    ? "Company added successfully."
                    : "Failed to add company. It may already exist or there was a database error.",
                StatusCode = success ? 200 : 400
            };
        }
    }
}
