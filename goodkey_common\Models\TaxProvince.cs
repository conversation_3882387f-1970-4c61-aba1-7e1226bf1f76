﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class TaxProvince
    {
        public int Id { get; set; }
        public int TaxTypeId { get; set; }
        public int ProvinceId { get; set; }
        public decimal? TaxRate { get; set; }
        public int? DisplayOrder { get; set; }
        public int? CreatedById { get; set; }
        public int? UpdatedById { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? UpdatedIsActiveDate { get; set; }
        public int? UpdatedIsActiveById { get; set; }

        public virtual AuthUser CreatedBy { get; set; }
        public virtual Provinces Province { get; set; }
        public virtual TaxType TaxType { get; set; }
        public virtual AuthUser UpdatedBy { get; set; }
        public virtual AuthUser UpdatedIsActiveBy { get; set; }
    }
}