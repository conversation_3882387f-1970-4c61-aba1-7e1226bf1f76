namespace goodkey_cms.Infrastructure.Helpers
{
    public static class PermissionClaimHelper
    {
        static public readonly string CLAIM = "permissionClaim";
        public static string Can(Action I, string A)
        {
            return $"CAN_{I.ToString().ToUpper()}_{<PERSON><PERSON>()}";
        }
    }

    public enum Action
    {

        View,
        Edit,
        Delete,
        Approve,
        Reject,
        Create,
        Perform,
        Archive,
        Manage,
        Access,
        Upload
    }

}